#[rustfmt::skip]
pub mod r#gen;

pub const PUBLIC_DIR: &str = "public";
pub const IMAGE_DIR: &str = "image";

pub const ADMIN_USERNAME: &str = "Admin";

pub use share::*;

#[allow(clippy::allow_attributes)]
mod share {
    // TODO: Parse use in build.rs

    pub const ENTITY_IDENT_MIN_LEN: usize = 1;
    pub const ENTITY_IDENT_MAX_LEN: usize = 128;

    // Artist

    pub const ARTIST_PROFILE_IMAGE_MIN_WIDTH: u32 = 256;
    pub const ARTIST_PROFILE_IMAGE_MAX_WIDTH: u32 = 4096;
    pub const ARTIST_PROFILE_IMAGE_MIN_HEIGHT: u32 = 256;
    pub const ARTIST_PROFILE_IMAGE_MAX_HEIGHT: u32 = 4096;
    pub const ARTIST_PROFILE_IMAGE_MIN_RATIO: f64 = 1.0;
    pub const ARTIST_PROFILE_IMAGE_MAX_RATIO: f64 = 2.0;

    // Release
    pub const RELEASE_COVER_IMAGE_MIN_WIDTH: u32 = 256;
    pub const RELEASE_COVER_IMAGE_MAX_WIDTH: u32 = 4096;
    pub const RELEASE_COVER_IMAGE_MIN_HEIGHT: u32 = 256;
    pub const RELEASE_COVER_IMAGE_MAX_HEIGHT: u32 = 4096;
    pub const RELEASE_COVER_IMAGE_MIN_RATIO: f64 = 1.0;
    pub const RELEASE_COVER_IMAGE_MAX_RATIO: f64 = 1.0;

    // User
    pub const AVATAR_MAX_FILE_SIZE: u64 = 10 * 1024 * 1024; // 10 mib
    pub const AVATAR_MIN_FILE_SIZE: u64 = 10 * 1024; // 10 kib

    pub const USER_PROFILE_BANNER_MAX_WIDTH: u32 = 1500;
    pub const USER_PROFILE_BANNER_MIN_WIDTH: u32 = 600;
    pub const USER_PROFILE_BANNER_MAX_HEIGHT: u32 = 500;
    pub const USER_PROFILE_BANNER_MIN_HEIGHT: u32 = 200;

    // Note: if you modify these values, please also change the regexes below
    #[allow(dead_code)]
    pub const USER_NAME_MIN_LENGTH: u8 = 1;
    #[allow(dead_code)]
    pub const USER_NAME_MAX_LENGTH: u8 = 64;
    pub const USER_NAME_REGEX_STR: &str = r"^[\p{L}\p{N}_]{1,64}$";
    #[allow(dead_code)]
    pub const USER_PASSWORD_MIN_LENGTH: u8 = 8;
    #[allow(dead_code)]
    pub const USER_PASSWORD_MAX_LENGTH: u8 = 64;
    pub const USER_PASSWORD_REGEX_STR: &str =
        r"^[A-Za-z\d`~!@#$%^&*()\-_=+]{8,64}$";
}

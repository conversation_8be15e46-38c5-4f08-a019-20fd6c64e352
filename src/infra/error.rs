use std::backtrace::Backtrace;
use std::borrow::Cow;
use std::fmt::Debug;

use argon2::password_hash;
use axum::http::StatusCode;
use macros::{ApiError, IntoErrorSchema};
use sea_orm::DbErr;

use super::database::error::FkViolation;
use crate::presentation::api_response::{
    IntoApiResponse, default_into_api_response_impl,
};

pub type Result<T> = std::result::Result<T, Error>;

/// Note: Don't impl from for variants
#[derive(Debug, thiserror::Error, ApiError, IntoErrorSchema)]
pub enum Error {
    #[error(transparent)]
    Internal(
        #[from]
        #[backtrace]
        InternalError,
    ),
    #[error(transparent)]
    User(
        #[from]
        #[backtrace]
        UserError,
    ),
}

// From<InternalError> for Error is auto-generated by derive_more::From

impl From<DbErr> for Error {
    fn from(source: DbErr) -> Self {
        Self::from(InternalError::from(source))
    }
}

impl Error {
    pub fn custom(message: impl Into<Cow<'static, str>>) -> Self {
        Self::from(InternalError::custom(message))
    }
}

impl From<password_hash::Error> for Error {
    fn from(source: password_hash::Error) -> Self {
        Self::from(InternalError::from(source))
    }
}

impl From<std::io::Error> for Error {
    fn from(source: std::io::Error) -> Self {
        Self::from(InternalError::from(source))
    }
}

impl From<tokio::task::JoinError> for Error {
    fn from(source: tokio::task::JoinError) -> Self {
        Self::from(InternalError::from(source))
    }
}

#[derive(Debug, thiserror::Error, ApiError)]
#[api_error(
    status_code = StatusCode::INTERNAL_SERVER_ERROR,
    into_response = self
)]
pub enum InternalError {
    #[error("Database error: {source}")]
    SeaOrm {
        #[from]
        source: DbErr,
        backtrace: Backtrace,
    },
    #[error("Tokio error: {source}")]
    Tokio {
        #[from]
        source: tokio::task::JoinError,
        backtrace: Backtrace,
    },
    #[error("IO error: {source}")]
    Io {
        #[from]
        source: std::io::Error,
        backtrace: Backtrace,
    },
    #[error("Password hash error: {source}")]
    PasswordHash {
        #[from]
        source: password_hash::Error,
        backtrace: Backtrace,
    },
    #[error("Custom error: {message}")]
    Custom {
        message: Cow<'static, str>,
        backtrace: Backtrace,
    },
}

impl InternalError {
    pub fn custom(message: impl Into<Cow<'static, str>>) -> Self {
        Self::Custom {
            message: message.into(),
            backtrace: Backtrace::capture(),
        }
    }
}

impl IntoApiResponse for InternalError {
    fn into_api_response(self) -> axum::response::Response {
        tracing::error!("{self}",);
        if let Some(backtrace) = std::error::request_ref::<Backtrace>(&self) {
            tracing::error!("Caused by:\n{backtrace}");
        }

        default_into_api_response_impl(self)
    }
}

#[derive(Debug, thiserror::Error, ApiError)]
pub enum UserError {
    #[error(transparent)]
    FkViolation {
        #[from]
        source: FkViolation<DbErr>,
    },
}

#[cfg(test)]
mod test {

    use axum::response::IntoResponse;
    use tracing_test::traced_test;

    use super::*;
    use crate::presentation::error::ApiError;

    // https://github.com/dbrgn/tracing-test/issues/48
    // This bug causes errors with line breaks cannot be captured
    // So I can only test the prefix of errors here
    // If this test fails, it may be because error messages has been changed
    #[tokio::test]
    #[traced_test]
    async fn test_nested_err_print() {
        let err = Error::from(DbErr::Custom("foobar".to_string()));
        let err = ApiError::Infra(err);

        let _ = err.into_response();

        assert!(logs_contain("Database error: Custom"));
        assert!(logs_contain("foobar"));

        // cranelift dosen't support catch_unwind yet
        // let err = ServiceError::Tokio(TokioError::TaskJoin(
        //     async {
        //         let handle = tokio::spawn(async {
        //             panic!("fake panic");
        //         });

        //         match handle.await {
        //             Err(e) => e,
        //             _ => unreachable!(),
        //         }
        //     }
        //     .await,
        // ));

        // let _ = err.into_response();

        // assert!(logs_contain("Tokio error: TaskJoin"));
    }
}

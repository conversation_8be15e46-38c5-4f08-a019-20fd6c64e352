[workspace]
members = [
    ".",
    "crates/collection_ext",
    "crates/entity",
    "crates/flow",
    "crates/libfp",
    "crates/macros",
    "crates/migration",
    "crates/proc_macros",
    "crates/fast_lrc",
]

[workspace.dependencies]
collection_ext = { path = "crates/collection_ext" }
entity         = { path = "crates/entity" }
fast-lrc       = { path = "crates/fast_lrc" }
flow           = { path = "crates/flow" }
libfp          = { path = "crates/libfp" }
macros         = { path = "crates/macros" }
migration      = { path = "crates/migration" }
proc_macros    = { path = "crates/proc_macros" }
# external
argon2 = "0.5"
axum = { version = "0.8", features = [
    "macros",
    "multipart",
] }
axum-login = { version = "0.18" }
chrono = "0.4"
dotenvy = "0.15"
enumset = { version = "1.1.7", features = [
    "alloc",
    "serde",
] }
itertools = "0.14"
quote = "1.0.38"
rand = "0.9.0"
sea-orm = { version = "1.1.10", features = [
    "sqlx-postgres",
    "runtime-tokio-rustls",
    "macros",
    "debug-print",
] }
sea-orm-migration = { features = [
    "runtime-tokio-rustls",
    "sqlx-postgres",
], version = "1.1.10" }
sea-query = "0.32.6"
serde = { version = "1", features = [
    "derive",
] }
syn = { version = "2.0.98", features = [
    "full",
] }
tokio = { version = "1", features = [
    "full",
] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = [
    "chrono",
] }
utoipa = { version = "5", features = [
    "axum_extras",
    "chrono",
    "preserve_order",
    "url",
] }

[build-dependencies]
dotenvy.workspace = true
git2              = "0.20.1"
quote.workspace   = true
syn.workspace     = true

[package]
edition = "2024"
name    = "thcdb_rs"
version = "0.1.0"

[dependencies]
collection_ext.workspace = true
entity.workspace         = true
flow.workspace           = true
libfp.workspace          = true
macros.workspace         = true
migration.workspace      = true
#
ammonia = "4.0.0"
argon2.features = [
    "std",
]
argon2.workspace = true
async-trait = { version = "0.1" }
axum.workspace = true
axum-extra = { version = "0.10.1", features = [ "query" ] }
axum-login.workspace = true
axum_typed_multipart = "0.16"
base64 = "0.22"
bon = { version = "3.0", features = [
    "implied-bounds",
] }
bytes = "1.10.1"
bytesize = { version = "2.0.1", features = [
    "serde",
] }
chrono.workspace = true
config = { version = "0.15.8", features = [
    "ron",
    "toml",
], default-features = false }
derive_more = { version = "2.0.1", features = [
    "display",
    "from",
    "into",
] }
dotenvy.workspace = true
enumset.workspace = true
fred = { version = "10.0" }
frunk = { version = "0.4.3", features = [
    "std",
] }
futures-util = "0.3"
governor = "0.8.0"
image = "0.25"
iso8601-duration = { version = "0.2", features = [
    "chrono",
] }
itertools.workspace = true
lettre = { version = "0.11.14", default-features = false, features = [
    "builder",
    "hostname",
    "pool",
    "smtp-transport",
    "tokio1-rustls-tls",
] }
maud = { version = "0.27.0", features = [
    "axum",
] }
nestify = "0.3.3"
pulldown-cmark = { version = "0.13.0", default-features = false, features = [
    "html",
    "simd",
] }
rand.workspace = true
regex = "1.11"
sea-orm.workspace = true
sea-orm-migration.workspace = true
sea-query.workspace = true
serde.workspace = true
serde_json = "1.0"
serde_repr = "0.1"
serde_with = { version = "3.12.0", features = [
    "chrono_0_4",
] }
smart-default = "0.7.1"
strum = { version = "0.27", features = [
    "derive",
] }
thiserror = "2.0.12"
tikv-jemallocator = { version = "0.6.0", optional = true }
tokio.workspace = true
tower = { version = "0.5.2", features = [
    "limit",
] }
tower-http = { version = "0.6.2", features = [
    "fs",
    "trace",
    "cors",
] }
tower-sessions-redis-store = "0.16.0"
tower_governor = "0.7.0"
tracing.workspace = true
tracing-subscriber.workspace = true
tracing-test = "0.2.5"
trait-variant = "0.1.2"
url = { version = "2.5.4", features = [
    "serde",
] }
utoipa.workspace = true
utoipa-axum = "0.2"
utoipa-scalar = { version = "0.3", features = [
    "axum",
] }
xxhash-rust = { version = "0.8", features = [
    "std",
    "xxh3",
] }
zxcvbn = "3.1"

[dev-dependencies]
rusty-hook = "=0.11.2"

# https://doc.rust-lang.org/cargo/reference/profiles.html
[profile.dev]
debug     = 0
opt-level = 1
strip     = "debuginfo"
# If you get incremental compilation ICE, disable it
# incremental     = false

[profile.dev.build-override]
opt-level = 3

[profile.ra]
incremental = false
inherits    = "dev"

[profile.release]
codegen-units   = 1
debug           = false
lto             = "thin"
opt-level       = 3
overflow-checks = true
panic           = "abort"

[features]
release = [
    "tikv-jemallocator",
]

name: Deploy Dev Server

on:
  push:
    branches:
      - main
    paths:
      - "crates/**"
      - "src/**"
      - "Cargo.toml"
      - "Cargo.lock"
      - "build.rs"
  workflow_dispatch:

env:
  # Disable incremental compilation
  CARGO_INCREMENTAL: 0
  # Disable debuginfo
  CARGO_PROFILE_DEV_DEBUG: 0
  RUSTC_WRAPPER: "sccache"
  SCCACHE_GHA_ENABLED: "true"

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    concurrency:
      group: "deploy-dev-server"
      cancel-in-progress: true

    steps:
      - name: Checkout source
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4

      - name: Set up Rust
        uses: dtolnay/rust-toolchain@nightly
        # Use build cache
      - uses: mozilla-actions/sccache-action@7d986dd989559c6ecdb630a3fd2557667be217ad

      - name: Install mold
        run: |
          sudo apt-get update
          sudo apt-get install -y mold clang

      - name: Build project
        run: cargo build
        env:
          RUSTFLAGS: "-C link-arg=-fuse-ld=mold"

      - name: Set up SCP
        run: |
          mkdir -v -m 700 $HOME/.ssh
          ssh-keyscan -H ${{ secrets.DEV_SSH_HOST }} >> $HOME/.ssh/known_hosts
          echo "${{ secrets.DEV_SSH_PRIVATE_KEY }}" > $HOME/.ssh/id_rsa
          chmod 600 $HOME/.ssh/id_rsa

      - name: Stop systemd Service
        run: |
          ssh -i $HOME/.ssh/id_rsa -o StrictHostKeyChecking=no ${{ secrets.DEV_SSH_USER }}@${{ secrets.DEV_SSH_HOST }} \
          "sudo systemctl stop thcdb.service"

      - name: Copy binary to server
        run: |
          scp target/debug/thcdb_rs \
          ${{ secrets.DEV_SSH_USER }}@${{ secrets.DEV_SSH_HOST }}:/home/<USER>//server/thcdb_rs

      - name: Restart systemd service
        run: |
          ssh -i $HOME/.ssh/id_rsa -o StrictHostKeyChecking=no ${{ secrets.DEV_SSH_USER }}@${{ secrets.DEV_SSH_HOST }} \
          "sudo systemctl restart thcdb.service"

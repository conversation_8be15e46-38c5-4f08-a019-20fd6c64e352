name: Lint

on:
  # push:
  #   branches: ["main"]
  pull_request:
    branches: ["main"]
    paths:
      - "crates/**"
      - "src/**"
      - "Cargo.toml"
      - "Cargo.lock"
      - "build.rs"
  workflow_dispatch:

env:
  # Disable incremental compilation
  CARGO_INCREMENTAL: 0
  # Disable debuginfo
  CARGO_TERM_COLOR: always
  RUSTC_WRAPPER: "sccache"
  SCCACHE_GHA_ENABLED: "true"
  RUSTFLAGS: "-W warnings"

jobs:
  rust:
    name: Rust
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        # Use build cache
      - uses: mozilla-actions/sccache-action@7d986dd989559c6ecdb630a3fd2557667be217ad # v0.0.9

      - name: Check format
        run: cargo fmt --check

      - name: Clippy
        run: cargo clippy --quiet

      - name: Build test
        run: cargo test --no-run

      - name: Run test
        run: cargo test --quiet

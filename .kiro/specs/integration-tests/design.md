# 设计文档

## 概述

本设计为 Touhou Cloud DB 项目添加使用 `postgresql_embedded` crate 的集成测试功能。该解决方案将提供一个完整的测试框架，包括自动化的嵌入式 PostgreSQL 数据库管理、测试工具和助手，以及针对数据库操作和 API 端点的全面测试覆盖。

## 架构

### 测试架构层次

```
tests/
├── integration/           # 集成测试模块
│   ├── common/           # 共享测试工具和助手
│   │   ├── mod.rs       # 模块导出
│   │   ├── database.rs  # 数据库设置和管理
│   │   ├── fixtures.rs  # 测试数据工厂
│   │   ├── client.rs    # API 客户端助手
│   │   └── assertions.rs # 自定义断言
│   ├── database/        # 数据库集成测试
│   │   ├── mod.rs
│   │   ├── repositories/ # 存储库测试
│   │   └── migrations.rs # 迁移测试
│   ├── api/             # API 端点测试
│   │   ├── mod.rs
│   │   ├── artist.rs    # 艺术家 API 测试
│   │   ├── release.rs   # 发行版 API 测试
│   │   ├── song.rs      # 歌曲 API 测试
│   │   └── auth.rs      # 认证 API 测试
│   └── lib.rs           # 测试库根文件
```

### 嵌入式数据库管理

使用 `postgresql_embedded` crate 提供：

- 自动下载和启动 PostgreSQL 实例
- 测试隔离的临时数据库
- 自动清理和资源管理
- 与现有 SeaORM 迁移系统集成

## 组件和接口

### 1. 数据库测试管理器 (`TestDatabase`)

```rust
pub struct TestDatabase {
    postgres: Postgres,
    connection: DatabaseConnection,
    database_url: String,
}

impl TestDatabase {
    pub async fn new() -> Result<Self, TestError>;
    pub async fn reset(&self) -> Result<(), TestError>;
    pub fn connection(&self) -> &DatabaseConnection;
    pub fn url(&self) -> &str;
}
```

**职责：**

- 管理嵌入式 PostgreSQL 实例生命周期
- 提供数据库连接和 URL
- 处理测试之间的数据库重置
- 运行迁移和种子数据

### 2. 测试应用程序工厂 (`TestApp`)

```rust
pub struct TestApp {
    pub database: TestDatabase,
    pub app: Router,
    pub client: TestClient,
    pub config: TestConfig,
}

impl TestApp {
    pub async fn new() -> Result<Self, TestError>;
    pub async fn reset(&self) -> Result<(), TestError>;
}
```

**职责：**

- 创建完整的应用程序实例用于测试
- 集成测试数据库和应用程序状态
- 提供 HTTP 客户端进行 API 测试
- 管理测试配置

### 3. 测试数据工厂 (`TestFixtures`)

```rust
pub struct TestFixtures {
    db: Arc<DatabaseConnection>,
}

impl TestFixtures {
    pub async fn create_artist(&self, data: CreateArtistData) -> Result<Artist, TestError>;
    pub async fn create_release(&self, data: CreateReleaseData) -> Result<Release, TestError>;
    pub async fn create_song(&self, data: CreateSongData) -> Result<Song, TestError>;
    pub async fn create_user(&self, data: CreateUserData) -> Result<User, TestError>;
}
```

**职责：**

- 提供便捷的测试数据创建方法
- 处理复杂的实体关系设置
- 支持自定义和默认测试数据
- 确保数据一致性

### 4. API 测试客户端 (`TestClient`)

```rust
pub struct TestClient {
    server: TestServer,
    auth_token: Option<String>,
}

impl TestClient {
    pub fn new(app: Router) -> Self;
    pub async fn get(&self, path: &str) -> TestResponse;
    pub async fn post<T: Serialize>(&self, path: &str, body: T) -> TestResponse;
    pub async fn put<T: Serialize>(&self, path: &str, body: T) -> TestResponse;
    pub async fn delete(&self, path: &str) -> TestResponse;
    pub async fn authenticate(&mut self, credentials: AuthCredentials) -> Result<(), TestError>;
}
```

**职责：**

- 使用 `axum-test` 提供类型安全的 HTTP 客户端
- 处理认证和授权
- 简化 API 测试请求构建
- 支持各种 HTTP 方法和响应断言

## 数据模型

### 测试配置模型

```rust
#[derive(Debug, Clone)]
pub struct TestConfig {
    pub database_url: String,
    pub app_port: u16,
    pub redis_url: String,
    pub test_timeout: Duration,
}
```

### 测试错误类型

```rust
#[derive(Debug, thiserror::Error)]
pub enum TestError {
    #[error("Database error: {0}")]
    Database(#[from] sea_orm::DbErr),

    #[error("PostgreSQL embedded error: {0}")]
    PostgresEmbedded(#[from] postgresql_embedded::Error),

    #[error("HTTP client error: {0}")]
    Http(String),

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("Test setup error: {message}")]
    Setup { message: String },
}
```

## 错误处理

### 错误处理策略

1. **数据库错误**：包装 SeaORM 和 PostgreSQL 错误，提供上下文信息
2. **HTTP 错误**：处理网络和 API 响应错误
3. **测试设置错误**：捕获测试环境配置问题
4. **资源清理**：确保即使在错误情况下也能正确清理资源

### 错误恢复

- 数据库连接失败时自动重试
- 测试失败时自动清理资源
- 详细的错误日志记录用于调试

## 测试策略

### 测试分类

1. **数据库集成测试**

   - 存储库 CRUD 操作测试
   - 复杂查询和连接测试
   - 事务和约束测试
   - 迁移测试

2. **API 集成测试**

   - REST 端点功能测试
   - 请求验证测试
   - 认证和授权测试
   - 错误处理测试

3. **端到端测试**
   - 完整用户工作流测试
   - 跨模块集成测试
   - 性能和负载测试

### 测试隔离策略

1. **数据库隔离**：每个测试使用独立的数据库事务或数据库实例
2. **并行执行**：支持并行测试执行，避免资源冲突
3. **状态清理**：测试之间自动清理状态和数据
4. **资源管理**：自动管理数据库连接和嵌入式实例

### 测试数据管理

1. **工厂模式**：使用工厂方法创建一致的测试数据
2. **种子数据**：预定义的基础数据集用于测试
3. **随机数据**：使用 faker 库生成随机但有效的测试数据
4. **关系处理**：自动处理复杂的实体关系

## 实现细节

### 依赖项集成

```toml
[dev-dependencies]
postgresql_embedded = "0.15"
axum-test = "15.8"
tokio-test = "0.4"
fake = { version = "2.9", features = ["derive"] }
```

### 测试宏和助手

```rust
// 自定义测试宏，自动设置和清理测试环境
#[macro_export]
macro_rules! integration_test {
    ($test_fn:ident) => {
        #[tokio::test]
        async fn $test_fn() {
            let test_app = TestApp::new().await.expect("Failed to setup test app");

            // 运行实际测试
            let result = $test_fn(&test_app).await;

            // 清理
            test_app.cleanup().await.expect("Failed to cleanup test app");

            result.expect("Test failed");
        }
    };
}
```

### 配置管理

- 测试专用配置文件 (`config.test.toml`)
- 环境变量覆盖支持
- 自动端口分配避免冲突
- 测试超时配置

### CI/CD 集成

- GitHub Actions 工作流配置
- 缓存策略优化构建时间
- 并行测试执行
- 测试报告生成和上传

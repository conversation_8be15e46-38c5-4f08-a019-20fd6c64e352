# Project Structure & Architecture

## Quick Overview

The project follows domain-driven design with clean architecture:

```
src/
├── application/     # Application services and use cases
├── domain/         # Core business logic and models
├── infra/          # Infrastructure concerns
└── presentation/   # API endpoints and controllers
```

## Architecture Documentation

For detailed architecture information, see:

- **[Design Patterns](../../.agent/architecture/patterns.md)** - DDD principles and code organization
- **[Project Structure](../../.agent/architecture/structure.md)** - Workspace and module organization
- **[Layer Architecture](../../.agent/architecture/layers.md)** - Detailed layer explanations
- **[Development Workflow](../../.agent/architecture/workflow.md)** - Step-by-step development process

## Related Documentation

- [Technology Stack](../../.agent/technology/stack.md) - Core technologies
- [Infrastructure Details](../../.agent/technology/infrastructure.md) - Database and services
- [Development Commands](../../.agent/development/commands.md) - Build and development tools
- [Quality Standards](../../.agent/quality/standards.md) - Code quality guidelines

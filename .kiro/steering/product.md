# Product Overview

Touhou Cloud DB is a collaborative platform for tagging and organizing doujin music. The goal is to build an open-access database providing comprehensive information on hundreds of thousands of tracks with a user-friendly interface.

## Quick Summary

- **Core Features**: Artist Management, Release Cataloging, Song Details, Event Tracking, Tag System, User Content
- **Target Users**: Doujin music enthusiasts, contributors, researchers
- **Project Context**: Community-driven prototype for the doujin music community

## Detailed Documentation

For comprehensive product information, see:

- **[Complete Product Vision](../../.agent/product/overview.md)** - Detailed vision, goals, and project context
- **[Feature Specifications](../../.agent/product/features.md)** - In-depth feature descriptions and capabilities
- **[User Personas](../../.agent/product/users.md)** - Target user groups and their needs

## Related Documentation

- [Architecture Overview](../../.agent/architecture/patterns.md) - How the product is built
- [Technology Stack](../../.agent/technology/stack.md) - Technical foundation
- [Development Setup](../../.agent/development/setup.md) - Getting started with development

# Technology Stack

## Core Technologies

- **Language**: Rust (2024 edition)
- **Web Framework**: Axum with async/await
- **Database**: PostgreSQL with SeaORM
- **Caching**: Redis via Fred client
- **Authentication**: axum-login with Argon2 password hashing

## Quick Commands

```bash
just check        # Run all checks (format, lint, test)
just fmt          # Format code
just migrate      # Run database migrations
just generate     # Regenerate SeaORM entities
```

## Technology Documentation

For comprehensive technical information, see:

- **[Core Stack](../../.agent/technology/stack.md)** - Detailed technology choices and rationale
- **[Libraries](../../.agent/technology/libraries.md)** - Key dependencies and frameworks
- **[Infrastructure](../../.agent/technology/infrastructure.md)** - Database, caching, and services

## Development Documentation

- **[Development Setup](../../.agent/development/setup.md)** - Environment configuration
- **[Build Commands](../../.agent/development/commands.md)** - Complete command reference
- **[Code Formatting](../../.agent/development/formatting.md)** - Style and formatting standards
- **[CI/CD Guidelines](../../.agent/development/cicd.md)** - Continuous integration practices

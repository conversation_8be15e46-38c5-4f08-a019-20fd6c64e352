# 用户系统表结构

## user - 用户表
用户的基本信息表，存储用户的核心数据。

主要字段说明：
- id: 用户唯一标识
- email: 用户邮箱
- username: 用户名
- password_hash: 密码哈希
- created_at: 创建时间
- updated_at: 更新时间

## user_role - 用户角色表
用户的角色权限关联表。

主要字段说明：
- user_id: 用户ID
- role: 角色类型（如管理员、普通用户等）

## user_following - 用户关注关系表
存储用户之间的关注关系。

主要字段说明：
- follower_id: 关注者用户ID
- following_id: 被关注者用户ID
- created_at: 关注时间

## user_list - 用户列表
用户创建的自定义列表。

主要字段说明：
- id: 列表ID
- user_id: 创建者用户ID
- name: 列表名称
- description: 列表描述
- visibility: 可见性设置

## user_list_item - 用户列表项
列表中的具体项目。

主要字段说明：
- list_id: 所属列表ID
- item_id: 项目ID（可能是歌曲、专辑等）
- item_type: 项目类型
- added_at: 添加时间

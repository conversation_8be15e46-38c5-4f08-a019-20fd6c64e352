# 艺术家相关表结构

## artist - 艺术家表
存储艺术家的基本信息。

主要字段说明：
- id: 艺术家唯一标识
- name: 艺术家名称
- description: 艺术家描述
- status: 状态
- type: 艺术家类型（个人/社团等）
- created_at: 创建时间
- updated_at: 更新时间

## artist_alias - 艺术家别名表
存储艺术家的其他名称或别名。

主要字段说明：
- artist_id: 艺术家ID
- alias: 别名
- primary: 是否为主要别名

## artist_localized_name - 艺术家本地化名称
存储艺术家在不同语言中的名称。

主要字段说明：
- artist_id: 艺术家ID
- language: 语言代码
- name: 本地化名称

## artist_membership - 艺术家成员关系表
管理艺术家之间的成员关系（如社团成员）。

主要字段说明：
- group_id: 社团ID
- member_id: 成员ID
- role_id: 角色ID

## artist_membership_role - 成员角色表
定义成员在社团中的角色。

主要字段说明：
- id: 角色ID
- name: 角色名称

## artist_membership_tenure - 成员任期表
记录成员在社团中的任期信息。

主要字段说明：
- membership_id: 成员关系ID
- start_date: 开始时间
- end_date: 结束时间

## artist_link - 艺术家相关链接
存储艺术家的相关网站链接。

主要字段说明：
- artist_id: 艺术家ID
- url: 链接地址
- type: 链接类型（如官网、社交媒体等）

## artist_history - 艺术家历史记录
记录艺术家信息的变更历史。

主要字段说明：
- id: 历史记录ID
- artist_id: 艺术家ID
- changed_at: 变更时间
- changed_by: 变更用户ID
- changes: 变更内容

其他相关历史记录表：
- artist_alias_history
- artist_link_history
- artist_localized_name_history
- artist_membership_history
- artist_membership_role_history
- artist_membership_tenure_history

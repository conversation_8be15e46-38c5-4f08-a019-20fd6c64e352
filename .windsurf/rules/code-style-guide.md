---
trigger: always_on
---

# Thcdb Server Development Guidelines

## Basic

- Create a todo list before you start editing
- Run `cargo check` after edit
- Don't write useless comments, e.g. just stating what was done without explaining why it was done
- Do not delete comments without permission

## Build Commands

- Check: `cargo check`
- Build: `cargo build` or `cargo build --release`
- Test all: `cargo test`
- Test single: `cargo test test_name`
- Test specific module: `cargo test module::test_name`
- Format: `cargo fmt`
- Lint: `cargo clippy`

## Code Style

- Use domain-driven design pattern (application/domain/infrastructure)

## CI/CD

- Don't fix warnings, only fix errors

# Testing Guidelines

## Quick Guidelines

- Avoid trivial or redundant tests
- Prefer concise, informative tests over exhaustive but shallow ones
- If a test documents a bug or regression, include a comment explaining its relevance

## Complete Testing Documentation

For comprehensive testing guidelines, see [.agent/quality/testing.md](../../.agent/quality/testing.md)

## Related Quality Documentation

- [Code Standards](../../.agent/quality/standards.md) - Overall code quality guidelines
- [Error Handling](../../.agent/quality/errors.md) - Error handling patterns

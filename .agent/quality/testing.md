# Testing Guidelines

## Core Testing Principles

### Meaningful Tests Only

- **Avoid trivial or redundant tests**: Do not write tests where the input and output are obviously identical or where the behavior is already guaranteed by language semantics or other tests
- **Every test should serve a distinct purpose**: Verify meaningful behavior, edge cases, or potential failure scenarios
- **Focus on business logic**: Test the important parts of your application, not trivial getters/setters

### Quality Over Quantity

- **Prefer concise, informative tests over exhaustive but shallow ones**: Each test should demonstrate a specific rule, corner case, or potential failure
- **Test behavior, not implementation**: Focus on what the code does, not how it does it
- **Readable test names**: Test names should clearly describe what is being tested

### Documentation Through Tests

- **If a test is meant to document a bug or regression, include a comment briefly explaining its relevance**
- **Tests as living documentation**: Good tests serve as examples of how code should be used
- **Edge case documentation**: Use tests to document known edge cases and their expected behavior

## Test Categories

### Unit Tests

Focus on testing individual functions, methods, or small components in isolation.

```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn artist_name_validation_rejects_empty_string() {
        let result = Artist::new("".to_string());
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), ArtistError::EmptyName);
    }

    #[test]
    fn artist_name_validation_accepts_valid_name() {
        let result = Artist::new("Valid Artist Name".to_string());
        assert!(result.is_ok());
    }

    // Example of documenting a specific bug fix
    #[test]
    fn artist_name_handles_unicode_characters() {
        // Regression test for issue #123: Unicode characters in artist names
        // were causing validation failures
        let result = Artist::new("東方Project".to_string());
        assert!(result.is_ok());
    }
}
```

### Integration Tests

Test the interaction between multiple components or layers.

```rust
#[tokio::test]
async fn artist_repository_crud_operations() {
    let db = setup_test_database().await;
    let repo = ArtistRepository::new(db);

    // Test create
    let artist = Artist::new("Test Artist".to_string()).unwrap();
    let created = repo.create(artist).await.unwrap();
    assert_eq!(created.name(), "Test Artist");

    // Test read
    let found = repo.find_by_id(created.id()).await.unwrap();
    assert_eq!(found.name(), "Test Artist");

    // Test update
    let updated = found.with_name("Updated Artist".to_string()).unwrap();
    repo.update(updated).await.unwrap();

    // Test delete
    repo.delete(created.id()).await.unwrap();
    let not_found = repo.find_by_id(created.id()).await;
    assert!(not_found.is_err());
}
```

### API Tests

Test HTTP endpoints and API behavior.

```rust
#[tokio::test]
async fn get_artist_returns_404_for_nonexistent_artist() {
    let app = create_test_app().await;

    let response = app
        .oneshot(
            Request::builder()
                .uri("/api/artists/99999")
                .body(Body::empty())
                .unwrap(),
        )
        .await
        .unwrap();

    assert_eq!(response.status(), StatusCode::NOT_FOUND);
}

#[tokio::test]
async fn create_artist_validates_required_fields() {
    let app = create_test_app().await;

    let invalid_payload = json!({
        "name": "" // Empty name should be rejected
    });

    let response = app
        .oneshot(
            Request::builder()
                .method(http::Method::POST)
                .uri("/api/artists")
                .header(http::header::CONTENT_TYPE, mime::APPLICATION_JSON.as_ref())
                .body(Body::from(invalid_payload.to_string()))
                .unwrap(),
        )
        .await
        .unwrap();

    assert_eq!(response.status(), StatusCode::BAD_REQUEST);
}
```

## Test Organization

### File Structure

```
src/
├── domain/
│   ├── artist/
│   │   ├── mod.rs
│   │   ├── model.rs
│   │   └── model/
│   │       └── test.rs          # Unit tests for domain models
│   └── ...
├── infra/
│   └── database/
│       └── artist/
│           ├── mod.rs
│           └── test.rs           # Integration tests for repositories
└── presentation/
    └── rest/
        └── artist/
            ├── mod.rs
            └── test.rs           # API endpoint tests
```

### Test Utilities

Create shared test utilities to reduce duplication:

```rust
// tests/common/mod.rs
pub async fn setup_test_database() -> DatabaseConnection {
    // Database setup logic
}

pub fn create_test_artist() -> Artist {
    Artist::new("Test Artist".to_string()).unwrap()
}

pub async fn create_test_app() -> Router {
    // Application setup for testing
}
```

## Testing Best Practices

### Test Data Management

- **Use factories or builders**: Create test data consistently
- **Isolate test data**: Each test should use its own data
- **Clean up after tests**: Ensure tests don't affect each other

### Async Testing

```rust
#[tokio::test]
async fn async_operation_test() {
    // Use tokio::test for async tests
    let result = some_async_operation().await;
    assert!(result.is_ok());
}
```

### Error Testing

```rust
#[test]
fn error_handling_test() {
    let result = operation_that_should_fail();

    // Test specific error types
    match result {
        Err(MyError::SpecificError { message }) => {
            assert_eq!(message, "Expected error message");
        }
        _ => panic!("Expected SpecificError"),
    }
}
```

### Property-Based Testing

For complex logic, consider property-based tests:

```rust
use proptest::prelude::*;

proptest! {
    #[test]
    fn artist_name_roundtrip(name in "[a-zA-Z0-9 ]{1,100}") {
        let artist = Artist::new(name.clone()).unwrap();
        assert_eq!(artist.name(), &name);
    }
}
```

## Test Coverage Guidelines

### What to Test

- **Business logic**: Core domain rules and validations
- **Error conditions**: How the system handles failures
- **Edge cases**: Boundary conditions and unusual inputs
- **Integration points**: Interactions between components

### What Not to Test

- **Trivial getters/setters**: Simple property access
- **Framework code**: Third-party library behavior
- **Generated code**: Auto-generated database entities
- **Obvious behavior**: Tests that just repeat the implementation

## Performance Testing

### Benchmark Tests

```rust
#[cfg(test)]
mod benches {
    use super::*;
    use std::time::Instant;

    #[test]
    fn benchmark_artist_search() {
        let start = Instant::now();

        // Perform operation
        let _result = expensive_search_operation();

        let duration = start.elapsed();

        // Assert reasonable performance
        assert!(duration.as_millis() < 100, "Search took too long: {:?}", duration);
    }
}
```

### Load Testing

For API endpoints, consider using tools like:

- **Artillery**: HTTP load testing
- **wrk**: Modern HTTP benchmarking tool
- **Custom Rust benchmarks**: Using criterion.rs

## Continuous Testing

### Pre-commit Testing

```bash
# Run before committing
cargo test                    # Run all tests
cargo test --release         # Test optimized builds
cargo test -- --nocapture    # Show test output
```

### CI/CD Integration

- **All tests must pass**: No exceptions for merging
- **Test in multiple environments**: Different OS, Rust versions
- **Performance regression testing**: Track performance over time
- **Flaky test detection**: Identify and fix unstable tests

## Debugging Tests

### Test Output

```rust
#[test]
fn debug_test() {
    let value = complex_calculation();
    println!("Debug value: {:?}", value); // Use println! for debugging
    assert_eq!(value, expected_value);
}
```

### Test-specific Logging

```rust
#[tokio::test]
async fn test_with_logging() {
    tracing_subscriber::fmt::init(); // Enable logging in tests

    let result = operation_with_logging().await;
    assert!(result.is_ok());
}
```

Remember: Good tests are an investment in code quality and maintainability. They should make development faster and more confident, not slower and more burdensome.

# Code Quality Standards

## Code Quality Principles

### Maintainability

- **Clear and expressive code**: Code should be self-documenting
- **Consistent naming conventions**: Use descriptive, consistent names
- **Appropriate abstraction levels**: Balance between DRY and readability
- **Modular design**: Small, focused functions and modules

### Reliability

- **Error handling**: Comprehensive error handling with meaningful messages
- **Input validation**: Validate all external inputs
- **Defensive programming**: Handle unexpected conditions gracefully
- **Resource management**: Proper cleanup of resources

### Performance

- **Efficient algorithms**: Choose appropriate data structures and algorithms
- **Memory management**: Leverage Rust's ownership system effectively
- **Async best practices**: Use async/await appropriately
- **Database optimization**: Efficient queries and connection management

## Rust-Specific Standards

### Ownership and Borrowing

```rust
// Prefer borrowing over cloning when possible
fn process_data(data: &[Item]) -> Result<Summary, ProcessError> {
    // Process without taking ownership
}

// Use owned types when necessary for API boundaries
fn create_item(name: String, description: String) -> Item {
    Item::new(name, description)
}

// Avoid unnecessary clones
fn bad_example(items: &[Item]) -> Vec<String> {
    items.iter().map(|item| item.name().clone()).collect() // Unnecessary clone
}

fn good_example(items: &[Item]) -> Vec<&str> {
    items.iter().map(|item| item.name()).collect() // Borrow instead
}
```

### Error Handling

```rust
// Use Result types for fallible operations
fn parse_artist_id(input: &str) -> Result<ArtistId, ParseError> {
    input.parse::<u64>()
        .map(ArtistId::new)
        .map_err(|_| ParseError::InvalidFormat)
}

// Provide context for errors
fn load_artist(id: ArtistId) -> Result<Artist, ArtistError> {
    database::find_artist(id)
        .map_err(|e| ArtistError::DatabaseError {
            id,
            source: e,
        })
}

// Use custom error types for domain-specific errors
#[derive(Debug, thiserror::Error)]
pub enum ArtistError {
    #[error("Artist with ID {id} not found")]
    NotFound { id: ArtistId },

    #[error("Database error for artist {id}")]
    DatabaseError {
        id: ArtistId,
        #[source]
        source: DatabaseError,
    },
}
```

### Type Safety

```rust
// Use newtype pattern for domain concepts
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub struct ArtistId(u64);

impl ArtistId {
    pub fn new(id: u64) -> Self {
        Self(id)
    }

    pub fn value(&self) -> u64 {
        self.0
    }
}

// Use enums for state representation
#[derive(Debug, Clone, PartialEq)]
pub enum ReleaseStatus {
    Draft,
    Published { date: DateTime<Utc> },
    Archived { reason: String },
}
```

## Documentation Standards

### Public API Documentation

````rust
/// Represents a doujin music artist or circle.
///
/// Artists can have multiple aliases and localized names.
/// They may be individual artists or music circles (groups).
///
/// # Examples
///
/// ```
/// use thcdb::domain::Artist;
///
/// let artist = Artist::new("ZUN".to_string())?;
/// assert_eq!(artist.name(), "ZUN");
/// ```
pub struct Artist {
    // fields...
}

impl Artist {
    /// Creates a new artist with the given name.
    ///
    /// # Arguments
    ///
    /// * `name` - The primary name of the artist
    ///
    /// # Errors
    ///
    /// Returns `ArtistError::EmptyName` if the name is empty or only whitespace.
    ///
    /// # Examples
    ///
    /// ```
    /// let artist = Artist::new("Team Shanghai Alice".to_string())?;
    /// ```
    pub fn new(name: String) -> Result<Self, ArtistError> {
        // implementation...
    }
}
````

### Internal Documentation

```rust
// Document complex business logic
fn calculate_similarity_score(artist1: &Artist, artist2: &Artist) -> f64 {
    // Similarity algorithm based on:
    // 1. Name similarity (Levenshtein distance)
    // 2. Shared releases (Jaccard index)
    // 3. Genre overlap (weighted by frequency)

    let name_similarity = levenshtein_similarity(artist1.name(), artist2.name());
    let release_similarity = jaccard_similarity(&artist1.releases(), &artist2.releases());
    let genre_similarity = genre_overlap_score(artist1, artist2);

    // Weighted combination: name (40%), releases (40%), genres (20%)
    name_similarity * 0.4 + release_similarity * 0.4 + genre_similarity * 0.2
}
```

## Performance Standards

### Database Operations

```rust
// Use efficient queries
impl ArtistRepository {
    // Good: Single query with joins
    async fn find_artists_with_releases(&self, limit: u32) -> Result<Vec<ArtistWithReleases>, DbError> {
        artists::Entity::find()
            .find_with_related(releases::Entity)
            .limit(limit)
            .all(&self.db)
            .await
            .map_err(Into::into)
    }

    // Avoid: N+1 queries
    async fn find_artists_with_releases_bad(&self, limit: u32) -> Result<Vec<ArtistWithReleases>, DbError> {
        let artists = artists::Entity::find().limit(limit).all(&self.db).await?;

        let mut result = Vec::new();
        for artist in artists {
            // This creates N additional queries!
            let releases = releases::Entity::find()
                .filter(releases::Column::ArtistId.eq(artist.id))
                .all(&self.db)
                .await?;
            result.push(ArtistWithReleases { artist, releases });
        }
        Ok(result)
    }
}
```

### Memory Management

```rust
// Use iterators for large datasets
fn process_large_dataset(items: &[Item]) -> Vec<ProcessedItem> {
    items
        .iter()
        .filter(|item| item.is_valid())
        .map(|item| process_item(item))
        .collect()
}

// Avoid collecting intermediate results unnecessarily
fn count_valid_items(items: &[Item]) -> usize {
    items.iter().filter(|item| item.is_valid()).count() // Don't collect here
}
```

## Security Standards

### Input Validation

```rust
// Validate all external inputs
pub fn create_artist_from_request(req: CreateArtistRequest) -> Result<Artist, ValidationError> {
    // Validate name
    if req.name.trim().is_empty() {
        return Err(ValidationError::EmptyName);
    }

    if req.name.len() > MAX_ARTIST_NAME_LENGTH {
        return Err(ValidationError::NameTooLong);
    }

    // Sanitize HTML content
    let sanitized_bio = sanitize_html(&req.bio);

    Artist::new(req.name.trim().to_string(), sanitized_bio)
}
```

### SQL Injection Prevention

```rust
// Use parameterized queries (SeaORM handles this automatically)
async fn find_artists_by_name(&self, name: &str) -> Result<Vec<Artist>, DbError> {
    artists::Entity::find()
        .filter(artists::Column::Name.contains(name)) // Safe with SeaORM
        .all(&self.db)
        .await
        .map_err(Into::into)
}
```

## Testing Standards

### Test Quality

- **Meaningful test names**: Describe what is being tested
- **Single responsibility**: Each test should verify one behavior
- **Arrange-Act-Assert**: Clear test structure
- **Independent tests**: Tests should not depend on each other

```rust
#[test]
fn artist_creation_fails_with_empty_name() {
    // Arrange
    let empty_name = "".to_string();

    // Act
    let result = Artist::new(empty_name);

    // Assert
    assert!(matches!(result, Err(ArtistError::EmptyName)));
}
```

## Code Review Standards

### Review Checklist

- [ ] **Functionality**: Does the code work as intended?
- [ ] **Architecture**: Does it follow project patterns and principles?
- [ ] **Performance**: Are there any performance concerns?
- [ ] **Security**: Are there security implications?
- [ ] **Tests**: Is there adequate test coverage?
- [ ] **Documentation**: Is public API documented?
- [ ] **Error Handling**: Are errors handled appropriately?
- [ ] **Code Style**: Does it follow project conventions?

### Review Guidelines

- **Be constructive**: Provide helpful feedback, not just criticism
- **Explain reasoning**: Help the author understand the why behind suggestions
- **Consider alternatives**: Discuss different approaches when appropriate
- **Focus on important issues**: Don't nitpick trivial style issues
- **Approve when ready**: Don't hold up good code for minor improvements

## Continuous Improvement

### Metrics to Track

- **Code coverage**: Maintain adequate test coverage
- **Cyclomatic complexity**: Keep functions reasonably simple
- **Technical debt**: Regular refactoring to address accumulated debt
- **Performance**: Monitor and address performance regressions

### Regular Practices

- **Code reviews**: All code changes reviewed by peers
- **Refactoring**: Regular cleanup and improvement of existing code
- **Documentation updates**: Keep documentation current with code changes
- **Dependency updates**: Regular updates to maintain security and performance

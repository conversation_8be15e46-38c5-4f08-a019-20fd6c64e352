# Technology Stack

## Core Technologies

### Language

- **Rust (2024 edition)**
  - Memory safety without garbage collection
  - High performance systems programming
  - Strong type system and ownership model
  - Excellent concurrency support

### Web Framework

- **Axum with async/await**
  - Modern async web framework
  - Built on tokio runtime
  - Excellent performance and ergonomics
  - Strong ecosystem integration

### Database

- **PostgreSQL with SeaORM**
  - Robust relational database
  - ACID compliance and reliability
  - Advanced features like full-text search
  - SeaORM for type-safe database operations

### Caching

- **Redis via Fred client**
  - In-memory data structure store
  - High-performance caching layer
  - Session storage and rate limiting
  - Pub/sub messaging capabilities

### Authentication

- **axum-login with Argon2 password hashing**
  - Secure session management
  - Industry-standard password hashing
  - Integration with Axum middleware
  - Configurable security parameters

## Runtime and Performance

### Async Runtime

- **Tokio**: High-performance async runtime
- **Async/await**: Modern asynchronous programming model
- **Futures**: Composable asynchronous operations

### Memory Management

- **Optional jemalloc allocator**: Enhanced memory allocation for release builds
- **Zero-copy operations**: Efficient data handling where possible
- **Ownership system**: Compile-time memory safety guarantees

## Development Tools

### Build System

- **Cargo**: Rust's built-in build system and package manager
- **Just**: Command runner for development tasks
- **Custom build scripts**: Specialized build configurations

### Code Quality

- **rustfmt**: Automatic code formatting
- **clippy**: Advanced linting and suggestions
- **cargo-audit**: Security vulnerability scanning

## Platform Support

### Target Platforms

- **Linux**: Primary deployment target
- **Docker**: Containerized deployment
- **Cross-platform development**: macOS and Windows support for development

### Deployment

- **Container-based**: Docker containerization
- **Environment configuration**: Flexible configuration management
- **Health checks**: Built-in monitoring and health endpoints

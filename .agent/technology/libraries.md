# Key Libraries

## API and Web Services

### API Documentation

- **utoipa (OpenAPI/Swagger)**
  - Automatic API documentation generation
  - OpenAPI 3.0 specification support
  - Integration with Rust type system
  - Interactive documentation interface

### Serialization

- **serde with JSON support**
  - High-performance serialization framework
  - JSON, YAML, TOML format support
  - Derive macros for automatic implementation
  - Custom serialization strategies

### Rate Limiting

- **tower-governor**
  - Middleware-based rate limiting
  - Multiple rate limiting algorithms
  - Integration with Axum middleware stack
  - Configurable limits and windows

## Content Processing

### Image Processing

- **image crate**
  - Comprehensive image format support
  - Image manipulation and transformation
  - Thumbnail generation
  - Format conversion capabilities

### HTML Templates

- **maud**
  - Compile-time HTML templating
  - Type-safe template generation
  - Rust syntax integration
  - High performance rendering

### Markdown Processing

- **pulldown-cmark**
  - CommonMark-compliant markdown parser
  - HTML output generation
  - Extension support
  - Security-focused parsing

## Communication

### Email Services

- **lettre for SMTP**
  - Modern email sending library
  - SMTP protocol implementation
  - TLS/SSL support
  - Template integration

## Database and ORM

### SeaORM Ecosystem

- **sea-orm**: Main ORM functionality
- **sea-orm-migration**: Database migration management
- **sea-query**: Query builder
- **sqlx**: Async SQL toolkit (underlying driver)

## Utility Libraries

### Error Handling

- **anyhow**: Flexible error handling
- **thiserror**: Custom error type derivation
- **eyre**: Enhanced error reporting

### Async Utilities

- **tokio**: Async runtime and utilities
- **futures**: Future combinators and utilities
- **async-trait**: Async functions in traits

### Configuration

- **config**: Configuration management
- **serde**: Configuration deserialization
- **toml**: TOML format support

### Logging and Monitoring

- **tracing**: Structured logging and instrumentation
- **tracing-subscriber**: Log formatting and output
- **metrics**: Application metrics collection

## Development Tools

### Testing

- **tokio-test**: Async testing utilities
- **mockall**: Mock object generation
- **proptest**: Property-based testing

### Code Generation

- **proc-macro2**: Procedural macro utilities
- **quote**: Code generation helpers
- **syn**: Rust syntax parsing

## Security

### Cryptography

- **argon2**: Password hashing
- **rand**: Cryptographically secure random numbers
- **uuid**: UUID generation

### Authentication

- **axum-login**: Session management
- **tower-sessions**: Session middleware
- **jsonwebtoken**: JWT token handling (if needed)

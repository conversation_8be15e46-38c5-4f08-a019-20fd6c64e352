# Core Features

## Artist Management

Track doujin music artists and circles with comprehensive information including:

- Artist profiles and biographical information
- Circle memberships and relationships
- Artist aliases and localized names
- Artist images and visual assets
- Historical tracking of changes

## Release Cataloging

Comprehensive album and release database featuring:

- Complete release information and metadata
- Catalog numbers and release events
- Release artwork and images
- Track listings and organization
- Historical version tracking

## Song Details

In-depth individual track information including:

- Song metadata and credits
- Multiple language support for titles
- Lyrics management and storage
- Artist credits and roles
- Song relationships and connections

## Event Tracking

Convention and release event information covering:

- Event details and locations
- Release associations with events
- Alternative event names and localization
- Historical event data

## Tag System

Flexible music categorization system with:

- Hierarchical tag relationships
- Alternative tag names and aliases
- Tag history and evolution tracking
- Flexible categorization for diverse music styles

## User Content

Community features for contributors including:

- User profiles and authentication
- User-generated content and corrections
- Community collaboration tools
- User following and social features
- Content moderation and quality control

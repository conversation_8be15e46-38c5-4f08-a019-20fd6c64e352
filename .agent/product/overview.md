# Product Overview

Touhou Cloud DB is a collaborative platform for tagging and organizing doujin music. The goal is to build an open-access database providing comprehensive information on hundreds of thousands of tracks with a user-friendly interface.

## Project Vision

The project aims to create an open-access database that serves the doujin music community by providing comprehensive, well-organized information about hundreds of thousands of tracks.

## Project Context

This is a community-driven project with skilled programmers having limited availability and passionate music fans learning to code. The focus is on building a prototype that serves the doujin music community.

## Key Characteristics

- **Community-Driven**: Built by and for the doujin music community
- **Open Access**: Freely available database for all users
- **Comprehensive**: Detailed information on hundreds of thousands of tracks
- **User-Friendly**: Intuitive interface for both contributors and consumers
- **Collaborative**: Enables community participation in data curation

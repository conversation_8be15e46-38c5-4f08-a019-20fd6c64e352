# Architecture Layers

## Application Layer (`src/application/`)

### Purpose

- Business use cases and application services
- Orchestrates domain objects and infrastructure
- Coordinates between domain and infrastructure layers

### Responsibilities

- **Use Case Implementation**: Implements specific business use cases
- **Transaction Management**: Manages database transactions and consistency
- **Service Orchestration**: Coordinates multiple domain services
- **Input Validation**: Validates application-level business rules
- **Error Handling**: Translates domain errors to application errors

### Modules

- `artist/` - Artist management use cases
- `auth/` - Authentication and authorization services
- `correction/` - Data correction workflows
- `event/` - Event management services
- `label/` - Label management functionality
- `release/` - Release cataloging services
- `song/` - Song management use cases
- `tag/` - Tagging and categorization services
- `user_*/` - User-related application services

## Domain Layer (`src/domain/`)

### Purpose

- Core business logic and domain models
- Repository interfaces
- Domain services
- Independent of external concerns

### Responsibilities

- **Business Logic**: Core business rules and domain logic
- **Domain Models**: Rich domain objects with behavior
- **Repository Interfaces**: Abstract data access contracts
- **Domain Services**: Complex business operations
- **Domain Events**: Business event definitions

### Key Components

- **Entities**: Core business objects with identity
- **Value Objects**: Immutable objects representing concepts
- **Aggregates**: Consistency boundaries for related entities
- **Domain Services**: Stateless business operations
- **Repository Interfaces**: Data access abstractions

## Infrastructure Layer (`src/infra/`)

### Purpose

- Database implementations (SeaORM)
- External service integrations
- Configuration management
- Storage, email, Redis implementations

### Responsibilities

- **Data Persistence**: Database operations and ORM integration
- **External APIs**: Third-party service integrations
- **File Storage**: Image and file management
- **Caching**: Redis-based caching implementations
- **Email Services**: SMTP and email delivery
- **Configuration**: Environment and configuration management

### Subdirectories

- `database/` - Database implementations and SeaORM integration
- `storage/` - File and image storage implementations
- `mapper/` - Data transformation between layers

## Presentation Layer (`src/presentation/`)

### Purpose

- REST API endpoints
- Request/response models
- API documentation
- Middleware and extractors

### Responsibilities

- **HTTP Endpoints**: REST API route definitions
- **Request Handling**: HTTP request parsing and validation
- **Response Formatting**: JSON response serialization
- **API Documentation**: OpenAPI/Swagger documentation
- **Middleware**: Cross-cutting concerns like authentication
- **Error Handling**: HTTP error response formatting

### Components

- **Controllers**: HTTP request handlers
- **DTOs**: Data transfer objects for API
- **Middleware**: Request/response processing
- **Extractors**: Custom request data extraction
- **Documentation**: API specification and examples

## Layer Interaction Rules

### Dependency Direction

- **Presentation** → **Application** → **Domain**
- **Infrastructure** → **Domain** (implements interfaces)
- **Domain** has no outward dependencies

### Communication Patterns

- **Synchronous**: Direct method calls within layers
- **Asynchronous**: Event-driven communication for cross-cutting concerns
- **Interface-Based**: All external dependencies through interfaces

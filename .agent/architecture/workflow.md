# Development Workflow

## Standard Development Process

### 1. Create/Modify Domain Models First

- Start with domain layer changes
- Define or update domain entities and value objects
- Implement business logic and domain services
- Ensure domain models are independent of external concerns

### 2. Update Database Migrations if Needed

- Create new migration files for schema changes
- Use timestamped migration naming convention
- Include both up and down migration scripts
- Test migrations in development environment

### 3. Regenerate Entities with `just generate`

- Run entity generation after database schema changes
- Verify generated entities match expected structure
- Update any custom entity extensions if needed
- Commit generated entity changes

### 4. Implement Repository Interfaces

- Create or update repository interfaces in domain layer
- Implement concrete repositories in infrastructure layer
- Ensure proper error handling and transaction management
- Add repository tests for data access logic

### 5. Add Application Services

- Implement use cases in application layer
- Coordinate between domain services and repositories
- Handle application-level validation and business rules
- Manage transactions and error handling

### 6. Create API Endpoints

- Add REST endpoints in presentation layer
- Define request/response DTOs
- Implement proper HTTP status codes and error handling
- Add API documentation with OpenAPI annotations

### 7. Run `just check` Before Committing

- Execute comprehensive checks before code submission
- Includes formatting, linting, and testing
- Ensures code quality standards are met
- Prevents broken builds in main branch

## Quality Gates

### Pre-Commit Checks

- **Formatting**: Code must be properly formatted
- **Linting**: No clippy warnings or errors
- **Testing**: All tests must pass
- **Documentation**: Public APIs must be documented

### Code Review Process

- **Domain Logic Review**: Verify business logic correctness
- **Architecture Compliance**: Ensure layer separation is maintained
- **Test Coverage**: Adequate test coverage for new functionality
- **Performance Considerations**: Review for potential performance issues

## Branch Strategy

### Feature Development

- Create feature branches from main
- Use descriptive branch names
- Keep branches focused on single features
- Regular rebasing to stay current with main

### Integration Process

- Run full test suite before merging
- Ensure migration compatibility
- Verify API documentation is updated
- Check for breaking changes

## Testing Strategy

### Unit Testing

- Test domain logic in isolation
- Mock external dependencies
- Focus on business rule validation
- Achieve high coverage for critical paths

### Integration Testing

- Test repository implementations
- Verify database operations
- Test API endpoints end-to-end
- Validate error handling scenarios

### Database Testing

- Test migrations up and down
- Verify entity generation accuracy
- Test complex queries and relationships
- Validate data integrity constraints

## Continuous Integration

### Automated Checks

- **Build Verification**: Ensure code compiles
- **Test Execution**: Run full test suite
- **Code Quality**: Linting and formatting checks
- **Security Scanning**: Dependency vulnerability checks

### Deployment Pipeline

- **Staging Deployment**: Automatic deployment to staging
- **Migration Execution**: Run database migrations
- **Smoke Testing**: Basic functionality verification
- **Production Deployment**: Manual approval for production

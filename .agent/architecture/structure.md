# Project Structure

## Workspace Structure

```
crates/
├── entity/         # SeaORM database entities (auto-generated)
├── macros/         # Custom macros
├── migration/      # Database migrations
└── proc_macros/    # Procedural macros
```

### Crate Descriptions

#### `crates/entity/`

- **Purpose**: SeaORM database entities (auto-generated)
- **Content**: Database table representations as Rust structs
- **Generation**: Automatically generated from database schema
- **Usage**: Imported by other crates for database operations

#### `crates/macros/`

- **Purpose**: Custom macros for code generation and utilities
- **Content**: Declarative macros for common patterns
- **Usage**: Simplify repetitive code patterns across the project

#### `crates/migration/`

- **Purpose**: Database schema migrations
- **Content**: SQL migration files and Rust migration code
- **Organization**: Timestamped migration modules
- **Usage**: Database schema evolution and versioning

#### `crates/proc_macros/`

- **Purpose**: Procedural macros for advanced code generation
- **Content**: Complex compile-time code generation
- **Usage**: Advanced metaprogramming features

## Source Code Structure

```
src/
├── application/     # Application services and use cases
├── domain/         # Core business logic and models
├── infra/          # Infrastructure concerns
└── presentation/   # API endpoints and controllers
```

## Module Organization

### Entity-Based Structure

Each major domain entity has corresponding modules across layers:

- `artist/` - Artist management functionality
- `release/` - Release cataloging features
- `song/` - Individual track management
- `event/` - Event tracking capabilities
- `tag/` - Tagging and categorization
- `user_*/` - User-related features

### Cross-Cutting Concerns

- `auth/` - Authentication and authorization
- `correction/` - Data correction and moderation
- `image*/` - Image processing and management
- `shared/` - Common utilities and interfaces

## File Naming Conventions

- **Module files**: `mod.rs` for module declarations
- **Model files**: `model.rs` or `model/` directory for complex models
- **Repository files**: `repo.rs` for repository implementations
- **Service files**: Descriptive names based on functionality
- **Test files**: `test.rs` or inline tests with `#[cfg(test)]`

## Dependencies Between Modules

- **Domain**: Independent, no external dependencies
- **Application**: Depends on domain interfaces
- **Infrastructure**: Implements domain interfaces
- **Presentation**: Depends on application services

# Development Environment Setup

## Prerequisites

### Required Software

- **PostgreSQL**: Database server (version 12 or higher)
- **Redis**: Caching and session storage
- **Rust**: Programming language toolchain (latest stable)
- **Just**: Command runner for development tasks

### Installation Commands

#### PostgreSQL

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS with Homebrew
brew install postgresql
brew services start postgresql

# Create database and user
sudo -u postgres createdb thcdb
sudo -u postgres createuser --interactive thcdb_user
```

#### Redis

```bash
# Ubuntu/Debian
sudo apt install redis-server

# macOS with Homebrew
brew install redis
brew services start redis

# Verify installation
redis-cli ping
```

#### Rust Toolchain

```bash
# Install rustup (Rust installer)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Install stable toolchain
rustup install stable
rustup default stable

# Install additional components
rustup component add rustfmt clippy
```

#### Just Command Runner

```bash
# Using cargo
cargo install just

# macOS with Homebrew
brew install just

# Ubuntu/Debian (download binary)
wget -qO- https://github.com/casey/just/releases/latest/download/just-x86_64-unknown-linux-musl.tar.gz | tar -xz just
sudo mv just /usr/local/bin/
```

## Configuration

### Environment Variables

Create a `.env` file in the project root:

```bash
# Database configuration
DATABASE_URL=postgresql://thcdb_user:password@localhost/thcdb

# Redis configuration
REDIS_URL=redis://localhost:6379

# Application configuration
RUST_LOG=info
BIND_ADDRESS=127.0.0.1:3000

# Development settings
RUST_BACKTRACE=1
```

### Configuration File

The project uses `config.toml` for structured configuration:

```toml
[database]
url = "postgresql://thcdb_user:password@localhost/thcdb"
max_connections = 10
min_connections = 1

[redis]
url = "redis://localhost:6379"
pool_size = 10

[server]
host = "127.0.0.1"
port = 3000

[logging]
level = "info"
format = "json"
```

## Development Dependencies

### Additional Rust Tools

```bash
# Development tools
cargo install cargo-watch    # Auto-rebuild on file changes
cargo install cargo-expand   # Macro expansion
cargo install cargo-audit    # Security vulnerability scanner
cargo install cargo-outdated # Check for outdated dependencies

# Database tools
cargo install sea-orm-cli    # SeaORM command-line interface
```

### Optional Tools

```bash
# Performance profiling
cargo install flamegraph

# Code coverage
cargo install cargo-tarpaulin

# Documentation tools
cargo install mdbook         # For documentation generation
```

## Database Setup

### Initial Setup

```bash
# Run database migrations
just migrate

# Generate SeaORM entities
just generate

# Verify setup
cargo test
```

### Development Data

```bash
# Load sample data (if available)
psql -U thcdb_user -d thcdb -f scripts/sample_data.sql

# Or use custom seeding script
cargo run --bin seed_database
```

## IDE Configuration

### VS Code Extensions

- **rust-analyzer**: Rust language server
- **CodeLLDB**: Debugging support
- **Better TOML**: TOML file support
- **PostgreSQL**: Database management
- **Redis**: Redis client integration

### VS Code Settings

```json
{
  "rust-analyzer.cargo.features": "all",
  "rust-analyzer.checkOnSave.command": "clippy",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.clippy": true
  },
  "files.watcherExclude": {
    "**/target/**": true
  }
}
```

## Verification

### Test Installation

```bash
# Check Rust installation
rustc --version
cargo --version

# Check database connection
psql -U thcdb_user -d thcdb -c "SELECT version();"

# Check Redis connection
redis-cli ping

# Run project checks
just check
```

### Common Issues

#### Database Connection Issues

```bash
# Check PostgreSQL service
sudo systemctl status postgresql

# Reset database password
sudo -u postgres psql -c "ALTER USER thcdb_user PASSWORD 'newpassword';"
```

#### Redis Connection Issues

```bash
# Check Redis service
sudo systemctl status redis

# Test Redis connectivity
redis-cli -h localhost -p 6379 ping
```

#### Rust Compilation Issues

```bash
# Update Rust toolchain
rustup update

# Clean build cache
cargo clean

# Rebuild dependencies
cargo build
```

## Development Workflow

### Daily Startup

1. **Start Services**:

   ```bash
   # Start PostgreSQL and Redis (if not running as services)
   brew services start postgresql redis
   # or
   sudo systemctl start postgresql redis
   ```

2. **Update Dependencies**:

   ```bash
   git pull
   cargo update
   just migrate  # If there are new migrations
   ```

3. **Run Development Server**:
   ```bash
   cargo run
   # or with auto-reload
   cargo watch -x run
   ```

### Environment Validation

```bash
# Complete environment check
just check

# Database connectivity test
cargo test --test database_integration

# Redis connectivity test
cargo test --test redis_integration
```

## Performance Optimization

### Development Build Optimization

```toml
# In Cargo.toml [profile.dev]
[profile.dev]
opt-level = 1        # Some optimization for faster builds
debug = true         # Keep debug info
incremental = true   # Faster incremental builds
```

### Optional: jemalloc Allocator

For release builds, the project optionally uses jemalloc for better memory allocation performance. This is automatically configured and doesn't require additional setup.

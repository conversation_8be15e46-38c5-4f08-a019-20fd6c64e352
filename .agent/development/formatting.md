# Code Formatting Standards

## Rust Code Formatting

### rustfmt Configuration

- **Line Width**: 80 characters maximum
- **Indentation**: 4 spaces (no tabs)
- **Line Endings**: Unix newlines (LF)
- **Trailing Whitespace**: Automatically removed

### Key Formatting Rules

```rust
// Function definitions
fn function_name(
    param1: Type1,
    param2: Type2,
) -> ReturnType {
    // Function body
}

// Struct definitions
struct MyStruct {
    field1: Type1,
    field2: Type2,
}

// Match expressions
match value {
    Pattern1 => result1,
    Pattern2 => {
        // Multi-line block
        result2
    }
}
```

### Import Organization

```rust
// Standard library imports first
use std::collections::HashMap;
use std::sync::Arc;

// External crate imports
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;

// Local crate imports
use crate::domain::Artist;
use crate::infra::database::Repository;
```

## SQL Formatting

### dprint Configuration

- **Keywords**: Uppercase (SELECT, FROM, WHERE, etc.)
- **Indentation**: 2 spaces for nested queries
- **Line Separation**: 2 blank lines between queries
- **Alignment**: Align column lists and conditions

### SQL Style Examples

```sql
SELECT
    a.id,
    a.name,
    a.created_at
FROM artists a
WHERE a.active = true
    AND a.created_at > '2023-01-01'
ORDER BY a.name ASC;


INSERT INTO releases (
    title,
    artist_id,
    release_date
) VALUES (
    'Album Title',
    1,
    '2023-12-01'
);
```

## TOML Formatting

### taplo Configuration

- **Indentation**: 2 spaces
- **Array Formatting**: Multi-line for readability
- **Key Ordering**: Alphabetical within sections
- **Comments**: Preserved and properly aligned

### TOML Style Examples

```toml
[package]
name = "thcdb-server"
version = "0.1.0"
edition = "2021"

[dependencies]
axum = "0.7"
serde = { version = "1.0", features = ["derive"] }
tokio = { version = "1.0", features = [
    "full",
    "macros",
    "rt-multi-thread",
] }

[dev-dependencies]
tokio-test = "0.4"
```

## Configuration Files

### .rustfmt.toml

```toml
max_width = 80
hard_tabs = false
tab_spaces = 4
newline_style = "Unix"
use_small_heuristics = "Default"
reorder_imports = true
reorder_modules = true
remove_nested_parens = true
edition = "2021"
```

### .dprint.jsonc

```json
{
  "sql": {
    "keywordCase": "upper",
    "indentWidth": 2,
    "linesBetweenQueries": 2
  },
  "includes": ["**/*.sql"],
  "excludes": ["target/**"]
}
```

### .taplo.toml

```toml
include = ["**/*.toml"]
exclude = ["target/**"]

[formatting]
indent_string = "  "
array_trailing_comma = true
array_auto_expand = true
compact_arrays = false
```

## Editor Integration

### VS Code Settings

```json
{
  "rust-analyzer.rustfmt.extraArgs": ["--edition", "2021"],
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.clippy": true
  }
}
```

### Pre-commit Hooks

- Automatic formatting verification
- Reject commits with formatting issues
- Integration with git hooks
- CI/CD formatting checks

## Formatting Workflow

### Development Process

1. **Write Code**: Focus on functionality first
2. **Auto-format**: Use `cargo fmt` or editor integration
3. **Review**: Check formatting meets standards
4. **Commit**: Ensure all files are properly formatted

### Quality Gates

- **Pre-commit**: Formatting check before commit
- **CI/CD**: Automated formatting verification
- **Code Review**: Manual formatting review
- **Release**: Final formatting validation

## Common Formatting Issues

### Avoid These Patterns

```rust
// Too long lines
let very_long_variable_name = some_function_call_with_many_parameters(param1, param2, param3, param4);

// Inconsistent spacing
fn bad_spacing(param1:Type1,param2: Type2)->ReturnType{
    // body
}

// Poor import organization
use crate::domain::Artist;
use std::collections::HashMap;
use serde::Serialize;
```

### Prefer These Patterns

```rust
// Properly wrapped long lines
let very_long_variable_name = some_function_call_with_many_parameters(
    param1,
    param2,
    param3,
    param4,
);

// Consistent spacing
fn good_spacing(param1: Type1, param2: Type2) -> ReturnType {
    // body
}

// Organized imports
use std::collections::HashMap;

use serde::Serialize;

use crate::domain::Artist;
```

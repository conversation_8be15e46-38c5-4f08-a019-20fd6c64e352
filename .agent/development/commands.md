# Build Commands and Tools

## Standard Cargo Commands

### Basic Operations

```bash
cargo check       # Quick compile check without producing executable
cargo build       # Build project in debug mode
cargo build --release  # Build optimized release version
cargo test        # Run all tests
cargo fmt         # Format code according to rustfmt rules
cargo clippy      # Run linter and provide suggestions
```

### Testing Commands

```bash
cargo test                    # Run all tests
cargo test test_name         # Run specific test by name
cargo test module::test_name # Run test in specific module
cargo test --release        # Run tests in release mode
cargo test -- --nocapture   # Show println! output in tests
```

## Just Command Runner

Uses `just` command runner (justfile) for common development tasks:

### Primary Development Commands

```bash
just fmt          # Format code (taplo, dprint, cargo fmt)
just fix          # Fix code issues (cargo fix, clippy --fix)
just check        # Run all checks (format, lint, test)
just default      # Format and fix (default task when running just)
```

### Database Commands

```bash
just generate     # Regenerate SeaORM entities from database schema
just migrate      # Run database migrations
```

### Comprehensive Quality Checks

```bash
just check        # Complete quality check pipeline:
                  # - Code formatting verification
                  # - Linting with clippy
                  # - Full test suite execution
                  # - Documentation generation
```

## Development Workflow Commands

### Daily Development

1. **Start Development Session**:

   ```bash
   just fmt          # Format any changes
   cargo check       # Quick compilation check
   ```

2. **Before Committing**:

   ```bash
   just check        # Run complete quality checks
   ```

3. **Database Changes**:
   ```bash
   just migrate      # Apply new migrations
   just generate     # Regenerate entities
   cargo test        # Verify tests still pass
   ```

## Advanced Cargo Commands

### Performance and Optimization

```bash
cargo build --release              # Optimized build
cargo test --release              # Test with optimizations
cargo bench                       # Run benchmarks
cargo flamegraph                  # Profile performance (with flamegraph installed)
```

### Documentation

```bash
cargo doc                         # Generate documentation
cargo doc --open                 # Generate and open documentation
cargo doc --no-deps             # Generate docs without dependencies
```

### Dependency Management

```bash
cargo update                      # Update dependencies
cargo tree                       # Show dependency tree
cargo audit                      # Check for security vulnerabilities
cargo outdated                   # Check for outdated dependencies
```

### Development Tools

```bash
cargo watch -x check             # Auto-run check on file changes
cargo watch -x test              # Auto-run tests on file changes
cargo expand                     # Expand macros (with cargo-expand)
```

## Environment-Specific Commands

### Development Environment

```bash
cargo run                        # Run application in development mode
RUST_LOG=debug cargo run        # Run with debug logging
```

### Testing Environment

```bash
cargo test --workspace          # Test entire workspace
cargo test --lib               # Test library code only
cargo test --bins              # Test binary targets only
```

### Production Preparation

```bash
cargo build --release          # Production build
cargo test --release          # Test production build
just check                     # Final quality verification
```

## Custom Scripts and Automation

### Pre-commit Hooks

- Automatic formatting check
- Linting verification
- Test execution
- Documentation generation

### CI/CD Integration

- Automated quality checks on pull requests
- Dependency vulnerability scanning
- Performance regression testing
- Documentation deployment

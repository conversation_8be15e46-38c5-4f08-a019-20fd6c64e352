# Touhou Cloud DB

[![Ask DeepWiki](https://deepwiki.com/badge.svg)](https://deepwiki.com/TouhouCloudMusic/server)

<h2 style="text-align: left;">
    <a href="README.md">English</a> |
    <a href="./docs/zh_CN/README.md">中文</a> |
    <a href="./docs/ja/README.md">日本語</a>
</h2>

Touhou Cloud DB is a collaborative platform for tagging and organizing doujin music. Our goal is to build an open-access database that provides comprehensive information on hundreds of thousands of tracks. It offers a user-friendly, visually appealing front-end interface for users to search, browse, and edit information about music with ease.

## Features

- **Artist Management**: Track information about doujin music artists and circles
- **Release Cataloging**: Comprehensive database of albums and releases
- **Song Details**: In-depth information about individual tracks
- **Event Tracking**: Information about conventions and release events
- **Tag System**: Flexible tagging for music categorization
- **User Content**: Community features for contributors

## Technology Stack

- **Backend**: Rust with Axum web framework
- **Database**: PostgreSQL via SeaORM
- **Caching**: Redis
- **API**: RESTful endpoints with OpenAPI documentation

## Contribute to Touhou Cloud DB

Our project team consists of two main groups: skilled programmers who have limited availability, and passionate doujin music fans who have time but may lack technical skills. As our developers contribute when they can, many music enthusiasts have taken the initiative to learn programming and are building the prototype from scratch. Progress is steady, and our commitment to this project remains strong.

If you have expertise in front-end development, UI design, or database management—and the time to contribute—we would love your help to bring this project closer to completion. Together, we can make Touhou Cloud DB an invaluable resource for the doujin music community!

## Contribution Guidelines

See our contribution guidelines for detailed information:

- [Contributing Guide (English)](./docs/en_US/CONTRIBUTING.md)
- [开发指南 (中文)](./docs/zh_CN/CONTRIBUTING.md)
- [開発ガイド (日本語)](./docs/ja/CONTRIBUTING.md)

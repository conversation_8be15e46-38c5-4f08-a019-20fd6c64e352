# THCDB 实体文档

THCDB (The Hardcore Database) 系统实体的完整文档，这是一个专为音乐目录设计的复杂数据库系统。

## 系统概述

THCDB 是一个音乐数据库系统，支持：

- **多实体音乐目录** - 艺人、发行版本、歌曲、厂牌和活动
- **全面元数据** - 多语言支持、本地化和替代名称
- **用户生成内容** - 评论、修正和用户列表
- **丰富关系** - 艺人成员关系、歌曲制作人员和发行版本关联
- **历史追踪** - 所有实体变更的完整审计轨迹
- **媒体管理** - 图片存储和审批工作流
- **标签系统** - 灵活的分类和归类

## 文档结构

### 核心实体

- [艺人](core-entities/artist.md) - 个人表演者和团体
- [发行版本](core-entities/release.md) - 专辑、EP、单曲和其他音乐发行
- [歌曲](core-entities/song.md) - 单独的音乐作品
- [厂牌](core-entities/label.md) - 唱片公司和音乐出版商
- [活动](core-entities/event.md) - 音乐会、音乐节和其他音乐活动

### 用户和社区

- [用户](user-community/user.md) - 用户账户和个人资料
- [评论](user-community/comments.md) - 用户评论和讨论
- [修正](user-community/corrections.md) - 用户提交的数据修正
- [用户列表](user-community/user-lists.md) - 个人音乐收藏和播放列表

### 元数据和本地化

- [本地化](metadata/localization.md) - 多语言名称支持
- [标签](metadata/tags.md) - 流派、风格和描述性标签
- [语言](metadata/languages.md) - 语言定义和歌曲语言关联

### 媒体管理

- [图片](media/images.md) - 图片存储和元数据
- [图片队列](media/image-queue.md) - 图片审批和审核工作流

### 制作人员和角色

- [制作角色](credits-roles/credit-roles.md) - 歌曲和发行版本制作角色
- [艺人成员关系](credits-roles/artist-memberships.md) - 团体成员关系和角色
- [歌曲制作人员](credits-roles/song-credits.md) - 个人歌曲贡献追踪

### 系统组件

- [枚举类型](system/enums.md) - 系统范围的枚举类型
- [历史追踪](system/history-tracking.md) - 审计轨迹和版本控制系统
- [关系模式](system/relationships.md) - 实体关系模式

### 图表和概览

- [实体关系](diagrams/entity-relationships.md) - 可视化关系图
- [领域概览](diagrams/domain-overview.md) - 高级系统架构

## 主要特性

### 历史追踪

几乎每个实体都有对应的 `*_history` 表来追踪所有变更，提供完整的审计轨迹。

### 多语言支持

实体支持多语言的本地化名称和替代名称，使数据库具有国际化访问能力。

### 用户协作

修正系统允许用户提出变更建议，可以由审核员审核和批准。

### 灵活关系

系统支持实体之间的复杂关系，如具有特定角色和任期的艺人成员关系。

## 开始使用

1. 从[核心实体](core-entities/)开始了解主要数据结构
2. 查看[系统组件](system/)了解底层模式
3. 探索[实体关系](diagrams/entity-relationships.md)查看实体如何连接
4. 根据需要查看特定领域区域

## 实体命名约定

- **基础实体** - 核心数据表（如 `artist`、`release`、`song`）
- **历史实体** - 审计轨迹表（如 `artist_history`、`release_history`）
- **连接实体** - 多对多关系（如 `song_artist`、`release_artist`）
- **本地化实体** - 多语言支持（如 `artist_localized_name`）
- **队列实体** - 审批工作流（如 `image_queue`、`artist_image_queue`）

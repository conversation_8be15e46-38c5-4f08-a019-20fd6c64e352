# THCDB Entity Documentation

This documentation provides comprehensive information about the entities in the THCDB (The Hardcore Database) system, a sophisticated music database designed to catalog artists, releases, songs, and related metadata.

## System Overview

THCDB is a music database system that supports:

- **Multi-entity music catalog**: Artists, releases, songs, labels, and events
- **Comprehensive metadata**: Multi-language support, localization, and alternative names
- **User-generated content**: Comments, corrections, and user lists
- **Rich relationships**: Artist memberships, song credits, and release associations
- **History tracking**: Complete audit trail for all entity changes
- **Media management**: Image storage and approval workflows
- **Tagging system**: Flexible categorization and classification

## Documentation Structure

### Core Entities
- [Artists](core-entities/artist.md) - Individual performers and groups
- [Releases](core-entities/release.md) - Albums, EPs, singles, and other musical releases
- [Songs](core-entities/song.md) - Individual musical compositions
- [Labels](core-entities/label.md) - Record labels and music publishers
- [Events](core-entities/event.md) - Concerts, festivals, and other music events

### User & Community
- [Users](user-community/user.md) - User accounts and profiles
- [Comments](user-community/comments.md) - User comments and discussions
- [Corrections](user-community/corrections.md) - User-submitted data corrections
- [User Lists](user-community/user-lists.md) - Personal music collections and playlists

### Metadata & Localization
- [Localization](metadata/localization.md) - Multi-language name support
- [Tags](metadata/tags.md) - Genre, style, and descriptive tags
- [Languages](metadata/languages.md) - Language definitions and song language associations

### Media Management
- [Images](media/images.md) - Image storage and metadata
- [Image Queue](media/image-queue.md) - Image approval and moderation workflow

### Credits & Roles
- [Credit Roles](credits-roles/credit-roles.md) - Roles for song and release credits
- [Artist Memberships](credits-roles/artist-memberships.md) - Group membership and roles
- [Song Credits](credits-roles/song-credits.md) - Individual song contribution tracking

### System Components
- [Enums](system/enums.md) - System-wide enumeration types
- [History Tracking](system/history-tracking.md) - Audit trail and versioning system
- [Relationships](system/relationships.md) - Entity relationship patterns

### Diagrams & Overviews
- [Entity Relationships](diagrams/entity-relationships.md) - Visual relationship maps
- [Domain Overview](diagrams/domain-overview.md) - High-level system architecture

## Key Features

### History Tracking
Almost every entity has a corresponding `*_history` table that tracks all changes over time, providing a complete audit trail.

### Multi-language Support
Entities support localized names and alternative names in multiple languages, making the database internationally accessible.

### User Collaboration
The correction system allows users to propose changes that can be reviewed and approved by moderators.

### Flexible Relationships
The system supports complex relationships between entities, such as artist memberships with specific roles and tenures.

## Getting Started

1. Start with the [Core Entities](core-entities/) to understand the main data structures
2. Review [System Components](system/) to understand the underlying patterns
3. Explore [Relationships](diagrams/entity-relationships.md) to see how entities connect
4. Check specific domain areas based on your needs

## Entity Naming Conventions

- **Base entities**: Core data tables (e.g., `artist`, `release`, `song`)
- **History entities**: Audit trail tables (e.g., `artist_history`, `release_history`)
- **Junction entities**: Many-to-many relationships (e.g., `song_artist`, `release_artist`)
- **Localization entities**: Multi-language support (e.g., `artist_localized_name`)
- **Queue entities**: Approval workflows (e.g., `image_queue`, `artist_image_queue`)

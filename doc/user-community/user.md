# User (用户)

用户实体，代表系统中的注册用户账户和个人资料。

## 基本信息

| 字段 | 类型 | 说明 |
|------|------|------|
| id | 整数 | 唯一标识符 |
| name | 文本 | 用户名（唯一） |
| password | 文本 | 加密密码 |
| avatar_id | 整数 | 头像图片ID |
| last_login | 时间戳 | 最后登录时间 |
| profile_banner_id | 整数 | 个人资料横幅图片ID |
| bio | 文本 | 个人简介 |

## 关联关系

### 直接关联
- **comment** - 用户发表的评论
- **correction_revision** - 用户提交的修正版本
- **correction_user** - 用户参与的修正
- **user_following** - 用户关注关系
- **user_list** - 用户创建的列表
- **user_role** - 用户角色权限

### 间接关联
- **图片** - 通过头像和横幅关联
- **修正** - 通过修正参与关联
- **列表项目** - 通过用户列表关联

## 业务逻辑

### 用户身份
用户系统支持：

- **唯一用户名** - 每个用户有唯一的标识符
- **安全认证** - 加密密码存储
- **个人资料** - 头像、横幅和个人简介
- **活动追踪** - 最后登录时间记录

### 社交功能
- **关注系统** - 用户可以关注其他用户
- **评论系统** - 用户可以对内容发表评论
- **列表创建** - 用户可以创建和管理音乐列表
- **协作编辑** - 用户可以提交内容修正

### 权限管理
- **角色系统** - 通过 user_role 管理权限
- **修正参与** - 不同类型的修正参与角色
- **内容管理** - 基于角色的内容管理权限

## 相关实体

### 社交互动
- **评论** - 用户在各种内容上的评论
- **关注** - 用户之间的关注关系
- **列表** - 用户创建的音乐收藏和播放列表

### 内容贡献
- **修正** - 用户提交的数据修正和改进
- **修正版本** - 用户创建的修正版本
- **协作角色** - 在修正过程中的不同参与角色

### 个人资料
- **头像** - 用户个人资料图片
- **横幅** - 个人资料页面横幅图片
- **个人简介** - 用户自我介绍

## 使用示例

### 基本用户
```
name: "music_lover_123"
password: "[加密密码]"
avatar_id: 456
last_login: 2023-12-01 14:30:00
bio: "热爱摇滚和爵士乐的音乐爱好者"

相关数据:
- user_list: 创建的播放列表
- comment: 发表的评论
- user_following: 关注的其他用户
```

### 活跃贡献者
```
name: "music_editor"
password: "[加密密码]"
avatar_id: 789
profile_banner_id: 101
last_login: 2023-12-01 16:45:00
bio: "音乐数据库编辑，专注于古典音乐"

相关数据:
- correction_revision: 提交的多个修正
- user_role: 编辑权限
- correction_user: 作为审核者参与修正
```

### 社交用户
```
name: "concert_goer"
password: "[加密密码]"
avatar_id: 234
last_login: 2023-12-01 12:15:00
bio: "现场音乐爱好者，经常参加音乐会"

相关数据:
- user_following: 关注多个用户
- user_list: 创建"最佳现场专辑"列表
- comment: 在音乐会活动上发表评论
```

## 用户角色系统

通过 `user_role` 实体管理用户权限：

### 权限级别
- **普通用户** - 基本浏览和评论权限
- **贡献者** - 可以提交修正和建议
- **编辑者** - 可以审核和批准修正
- **管理员** - 完整的系统管理权限

### 专业角色
- **音乐专家** - 特定流派或时期的专家
- **图片管理员** - 管理图片上传和审核
- **社区管理员** - 管理用户互动和内容

## 关注系统

用户可以通过 `user_following` 关注其他用户：

### 关注功能
- **关注用户** - 跟踪其他用户的活动
- **粉丝系统** - 查看谁关注了自己
- **活动流** - 查看关注用户的最新活动
- **推荐系统** - 基于关注关系的内容推荐

### 社交发现
- **相似兴趣** - 发现有相似音乐品味的用户
- **专家关注** - 关注特定领域的专家
- **社区建设** - 围绕共同兴趣建立社区

## 用户列表

用户可以创建和管理各种类型的列表：

### 列表类型
- **播放列表** - 个人音乐收藏
- **愿望清单** - 想要收听的音乐
- **评分列表** - 个人音乐评分
- **主题列表** - 基于特定主题的收藏

### 列表功能
- **公开/私有** - 控制列表可见性
- **协作列表** - 与其他用户共同编辑
- **列表分享** - 与社区分享收藏
- **列表发现** - 发现其他用户的有趣列表

## 评论系统

用户可以对各种内容发表评论：

### 评论目标
- **修正** - 对数据修正的讨论
- **发行版本** - 对专辑和单曲的评论
- **艺人** - 对艺人的讨论
- **活动** - 对音乐会和音乐节的评论

### 评论管理
- **状态控制** - 可见、审核中、隐藏、删除
- **版本控制** - 评论的修改历史
- **社区调节** - 社区驱动的内容管理

## 修正参与

用户可以参与数据修正过程：

### 参与角色
- **作者** - 提交修正的用户
- **共同作者** - 协助修正的用户
- **审核者** - 审核修正的用户
- **批准者** - 最终批准修正的用户

### 协作工作流
- **修正提交** - 用户提交数据改进
- **同行审核** - 其他用户审核修正
- **讨论** - 通过评论讨论修正
- **批准流程** - 正式的修正批准过程

## 隐私和安全

用户系统包含隐私和安全功能：

### 数据保护
- **密码加密** - 安全的密码存储
- **个人信息** - 可选的个人资料信息
- **活动隐私** - 控制活动可见性

### 账户安全
- **登录追踪** - 最后登录时间记录
- **会话管理** - 安全的用户会话
- **权限控制** - 基于角色的访问控制

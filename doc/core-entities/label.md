# Label (厂牌)

唱片厂牌实体，代表唱片公司、音乐出版商和其他参与音乐制作、发行和推广的组织。

## 基本信息

| 字段                     | 类型 | 说明         |
| ------------------------ | ---- | ------------ |
| id                       | 整数 | 唯一标识符   |
| name                     | 文本 | 厂牌名称     |
| founded_date             | 日期 | 成立日期     |
| founded_date_precision   | 枚举 | 成立日期精度 |
| dissolved_date           | 日期 | 解散日期     |
| dissolved_date_precision | 枚举 | 解散日期精度 |

## 日期精度

- **Day** - 精确到日
- **Month** - 精确到月
- **Year** - 精确到年

## Relationships

### Direct Relationships (has_many)

- **label_founder**: Artists who founded or co-founded this label
- **label_localized_name**: Names in different languages
- **release_catalog_number**: Catalog numbers for releases on this label

### Indirect Relationships (via junction tables)

- **artists**: Through `label_founder` junction (founding artists)
- **releases**: Through `release_catalog_number` junction

## Business Logic

### Label Types

While not explicitly typed in the entity, labels can serve various functions:

- **Record Labels**: Primary release and distribution
- **Publishers**: Rights management and licensing
- **Distributors**: Physical and digital distribution
- **Imprints**: Sub-labels under larger parent companies
- **Independent Labels**: Artist-owned or small independent operations

### Lifecycle Management

Labels have a complete lifecycle:

- **Foundation**: When and by whom the label was started
- **Active Period**: The operational timespan
- **Dissolution**: When the label ceased operations (if applicable)

### Founder Relationships

The system tracks which artists founded labels, supporting:

- **Artist-owned labels**: Musicians who start their own labels
- **Collaborative foundations**: Multiple artists founding a label together
- **Historical context**: Understanding the origins and motivations behind labels

## Related Entities

### Core Associations

- **Founders**: Artists who established the label
- **Releases**: Music released under the label's catalog
- **Catalog Numbers**: Official release identifiers

### Metadata

- **Localized Names**: Support for label names in multiple languages and scripts
- **History Tracking**: Changes to label information over time

### Release Management

Labels connect to releases through catalog numbers, which provide:

- **Release Identification**: Official catalog numbering systems
- **Format Tracking**: Different formats (CD, vinyl, digital) with separate numbers
- **Regional Variations**: Different catalog numbers for different markets
- **Reissue Management**: New catalog numbers for reissues and remasters

## Usage Examples

### Independent Label

```
name: "Indie Records"
founded_date: 2010-03-15
founded_date_precision: Day
dissolved_date: null
dissolved_date_precision: Day

Related data:
- label_founder: Links to founding artist
- release_catalog_number: "IR001", "IR002", etc.
```

### Historical Label

```
name: "Classic Records"
founded_date: 1955-01-01
founded_date_precision: Year
dissolved_date: 1985-01-01
dissolved_date_precision: Year

Related data:
- release_catalog_number: Historical catalog numbers
- label_localized_name: Names in different languages
```

### Artist-Founded Label

```
name: "Artist's Own Label"
founded_date: 2020-06-01
founded_date_precision: Month
dissolved_date: null

Related data:
- label_founder: The founding artist
- release_catalog_number: Artist's releases and other signings
```

### Multi-language Label

```
name: "International Records"
founded_date: 1990-01-01
founded_date_precision: Year

Related data:
- label_localized_name:
  - English: "International Records"
  - French: "Disques Internationaux"
  - German: "Internationale Schallplatten"
```

## Catalog Number System

Labels manage releases through catalog numbers via the `release_catalog_number` entity:

### Numbering Schemes

- **Sequential**: IR001, IR002, IR003...
- **Format-based**: CD001, LP001, DIG001...
- **Year-based**: 2023-001, 2023-002...
- **Series-based**: ROCK-001, JAZZ-001...

### Multiple Formats

The same release can have multiple catalog numbers:

- CD version: "LABEL-CD-001"
- Vinyl version: "LABEL-LP-001"
- Digital version: "LABEL-DIG-001"

### Regional Variations

Different markets may have different catalog numbers:

- US release: "US-001"
- European release: "EU-001"
- Japanese release: "JP-001"

## Label Hierarchy

While not explicitly modeled, the system can represent label relationships through:

### Parent/Child Labels

- **Major Labels**: Large corporations with multiple imprints
- **Imprints**: Specialized sub-labels under major labels
- **Distribution Deals**: Independent labels distributed by majors

### Artist Relationships

- **Signed Artists**: Artists with recording contracts
- **Founding Artists**: Artists who started the label
- **Distributed Artists**: Artists whose releases are distributed by the label

## Historical Context

The label entity supports music industry history:

### Timeline Tracking

- **Foundation dates**: When labels were established
- **Dissolution dates**: When labels ceased operations
- **Active periods**: The operational lifespan of labels

### Industry Evolution

- **Independent movement**: Artist-founded labels
- **Major label consolidation**: Mergers and acquisitions
- **Digital transformation**: New distribution models

### Cultural Impact

- **Genre development**: Labels associated with specific musical movements
- **Regional scenes**: Local labels supporting regional music
- **Artist development**: Labels known for nurturing talent

## Integration with Releases

Labels connect to the broader music ecosystem through:

### Release Management

- **Catalog organization**: Systematic numbering and organization
- **Format management**: Different physical and digital formats
- **Reissue programs**: Re-releasing historical material

### Artist Development

- **Roster management**: Artists signed to the label
- **Release planning**: Coordinating artist releases
- **Promotion and distribution**: Marketing and getting music to market

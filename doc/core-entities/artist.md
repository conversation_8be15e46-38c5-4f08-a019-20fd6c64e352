# Artist (艺人)

音乐艺人实体，包括个人歌手、乐队、组合等音乐创作者和表演者。

## 基本信息

| 字段                      | 类型     | 说明            |
| ------------------------- | -------- | --------------- |
| id                        | 整数     | 唯一标识符      |
| name                      | 文本     | 艺人主要名称    |
| artist_type               | 枚举     | 艺人类型        |
| text_alias                | 文本数组 | 别名列表        |
| start_date                | 日期     | 成立/出生日期   |
| start_date_precision      | 枚举     | 开始日期精度    |
| end_date                  | 日期     | 解散/去世日期   |
| end_date_precision        | 枚举     | 结束日期精度    |
| current_location_country  | 文本     | 当前所在国家    |
| current_location_province | 文本     | 当前所在省份/州 |
| current_location_city     | 文本     | 当前所在城市    |
| start_location_country    | 文本     | 起源国家        |
| start_location_province   | 文本     | 起源省份/州     |
| start_location_city       | 文本     | 起源城市        |

## 艺人类型

- **Solo** - 个人艺人
- **Multiple** - 乐队/组合
- **Unknown** - 未知类型

## 日期精度

- **Day** - 精确到日
- **Month** - 精确到月
- **Year** - 精确到年

## Relationships

### Direct Relationships (has_many)

- **artist_alias_history**: Historical changes to aliases
- **artist_image**: Associated images (profile pictures, etc.)
- **artist_image_queue**: Pending image approvals
- **artist_link**: External links (websites, social media)
- **artist_localized_name**: Names in different languages
- **artist_membership_history**: History of group memberships
- **label_founder**: Labels founded by this artist
- **release_artist**: Releases associated with this artist
- **release_credit**: Credits on releases
- **release_track_artist**: Track-level artist associations
- **song_artist**: Songs associated with this artist
- **song_credit**: Credits on individual songs

### Indirect Relationships (via junction tables)

- **releases**: Through `release_artist` junction
- **songs**: Through `song_artist` junction
- **images**: Through `artist_image` junction
- **labels**: Through `label_founder` junction (for artists who founded labels)

## Business Logic

### Artist Types

- **Solo artists**: Individual performers (singers, musicians, producers)
- **Multiple artists**: Bands, orchestras, choirs, collaborations
- **Unknown**: Used when the artist type cannot be determined

### Location Tracking

The system tracks both origin and current location to handle:

- Artists who relocate during their career
- Touring artists with different home bases
- Historical context for music scenes and movements

### Date Precision

Different levels of date precision accommodate varying levels of historical information:

- Exact dates for well-documented modern artists
- Month/year for less documented periods
- Year-only for historical or poorly documented artists

## Related Entities

### Core Associations

- **Releases**: Artists can be associated with releases as main artists, featured artists, or contributors
- **Songs**: Artists can be composers, performers, or contributors to individual songs
- **Labels**: Artists can found or be associated with record labels

### Metadata

- **Localized Names**: Support for artist names in multiple languages and scripts
- **Images**: Profile pictures, promotional photos, and other visual media
- **Links**: Official websites, social media profiles, and other external references

### History Tracking

All changes to artist information are tracked through various history tables, providing a complete audit trail of modifications.

## Usage Examples

### Solo Artist

```
name: "John Doe"
artist_type: Solo
start_date: 1990-05-15
start_date_precision: Day
current_location_country: "United States"
current_location_city: "Nashville"
```

### Band

```
name: "The Example Band"
artist_type: Multiple
start_date: 2005-01-01
start_date_precision: Year
end_date: 2015-12-31
end_date_precision: Year
start_location_country: "United Kingdom"
start_location_city: "London"
```

### Historical Artist

```
name: "Classical Composer"
artist_type: Solo
start_date: 1756-01-01
start_date_precision: Year
end_date: 1791-01-01
end_date_precision: Year
start_location_country: "Austria"
```

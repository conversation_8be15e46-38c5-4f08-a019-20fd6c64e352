# Artist Entity

The `artist` entity represents individual performers, bands, groups, or any musical entity that creates or performs music.

## Entity Structure

```rust
pub struct Model {
    pub id: i32,                              // Primary key
    pub name: String,                         // Primary name (Text type)
    pub artist_type: ArtistType,              // Solo, Multiple, or Unknown
    pub text_alias: Option<Vec<String>>,      // Array of alternative names
    pub start_date: Option<Date>,             // Formation/birth date
    pub start_date_precision: Option<DatePrecision>, // Day/Month/Year precision
    pub end_date: Option<Date>,               // Dissolution/death date
    pub end_date_precision: Option<DatePrecision>,   // Day/Month/Year precision
    pub current_location_country: Option<String>,    // Current country
    pub current_location_province: Option<String>,   // Current province/state
    pub current_location_city: Option<String>,       // Current city
    pub start_location_country: Option<String>,      // Origin country
    pub start_location_province: Option<String>,     // Origin province/state
    pub start_location_city: Option<String>,         // Origin city
}
```

## Field Descriptions

### Core Fields
- **id**: Unique identifier for the artist
- **name**: The primary name of the artist (stored as Text for unlimited length)
- **artist_type**: Categorizes the artist type:
  - `Solo`: Individual performer
  - `Multiple`: Band, group, or collective
  - `Unknown`: Type not determined

### Alternative Names
- **text_alias**: Array of alternative names, nicknames, or aliases

### Date Information
- **start_date/end_date**: Formation/dissolution dates for groups, birth/death for individuals
- **start_date_precision/end_date_precision**: Indicates the precision of the date:
  - `Day`: Exact date known
  - `Month`: Month and year known
  - `Year`: Only year known

### Location Information
- **current_location_***: Where the artist is currently based
- **start_location_***: Where the artist originated or was formed
- Location is hierarchical: Country → Province/State → City

## Relationships

### Direct Relationships (has_many)
- **artist_alias_history**: Historical changes to aliases
- **artist_image**: Associated images (profile pictures, etc.)
- **artist_image_queue**: Pending image approvals
- **artist_link**: External links (websites, social media)
- **artist_localized_name**: Names in different languages
- **artist_membership_history**: History of group memberships
- **label_founder**: Labels founded by this artist
- **release_artist**: Releases associated with this artist
- **release_credit**: Credits on releases
- **release_track_artist**: Track-level artist associations
- **song_artist**: Songs associated with this artist
- **song_credit**: Credits on individual songs

### Indirect Relationships (via junction tables)
- **releases**: Through `release_artist` junction
- **songs**: Through `song_artist` junction
- **images**: Through `artist_image` junction
- **labels**: Through `label_founder` junction (for artists who founded labels)

## Business Logic

### Artist Types
- **Solo artists**: Individual performers (singers, musicians, producers)
- **Multiple artists**: Bands, orchestras, choirs, collaborations
- **Unknown**: Used when the artist type cannot be determined

### Location Tracking
The system tracks both origin and current location to handle:
- Artists who relocate during their career
- Touring artists with different home bases
- Historical context for music scenes and movements

### Date Precision
Different levels of date precision accommodate varying levels of historical information:
- Exact dates for well-documented modern artists
- Month/year for less documented periods
- Year-only for historical or poorly documented artists

## Related Entities

### Core Associations
- **Releases**: Artists can be associated with releases as main artists, featured artists, or contributors
- **Songs**: Artists can be composers, performers, or contributors to individual songs
- **Labels**: Artists can found or be associated with record labels

### Metadata
- **Localized Names**: Support for artist names in multiple languages and scripts
- **Images**: Profile pictures, promotional photos, and other visual media
- **Links**: Official websites, social media profiles, and other external references

### History Tracking
All changes to artist information are tracked through various history tables, providing a complete audit trail of modifications.

## Usage Examples

### Solo Artist
```
name: "John Doe"
artist_type: Solo
start_date: 1990-05-15
start_date_precision: Day
current_location_country: "United States"
current_location_city: "Nashville"
```

### Band
```
name: "The Example Band"
artist_type: Multiple
start_date: 2005-01-01
start_date_precision: Year
end_date: 2015-12-31
end_date_precision: Year
start_location_country: "United Kingdom"
start_location_city: "London"
```

### Historical Artist
```
name: "Classical Composer"
artist_type: Solo
start_date: 1756-01-01
start_date_precision: Year
end_date: 1791-01-01
end_date_precision: Year
start_location_country: "Austria"
```

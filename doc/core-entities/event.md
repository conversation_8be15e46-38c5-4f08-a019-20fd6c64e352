# Event Entity

The `event` entity represents musical events such as concerts, festivals, album launches, recording sessions, and other music-related happenings.

## Entity Structure

```rust
pub struct Model {
    pub id: i32,                            // Primary key
    pub name: String,                       // Event name (Text type)
    pub short_description: String,          // Brief description (Text type)
    pub description: String,                // Full description (Text type)
    pub start_date: Option<Date>,           // Event start date
    pub start_date_precision: DatePrecision, // Start date precision
    pub end_date: Option<Date>,             // Event end date
    pub end_date_precision: DatePrecision,   // End date precision
}
```

## Field Descriptions

### Core Fields
- **id**: Unique identifier for the event
- **name**: The primary name of the event (stored as Text for unlimited length)
- **short_description**: A brief summary of the event
- **description**: Detailed description of the event

### Date Information
- **start_date**: When the event begins
- **end_date**: When the event ends (for multi-day events)
- **Date precision levels**:
  - `Day`: Exact date known
  - `Month`: Month and year known
  - `Year`: Only year known

## Relationships

### Direct Relationships (has_many)
- **event_alternative_name**: Alternative names for the event
- **release_event**: Releases associated with this event
- **release_event_history**: Historical release-event associations

### Indirect Relationships (via junction tables)
- **releases**: Through `release_event` junction

## Business Logic

### Event Types
While not explicitly typed, events can represent various types of musical happenings:

- **Concerts**: Individual performances by artists
- **Festivals**: Multi-artist, multi-day events
- **Album Launches**: Release parties and promotional events
- **Recording Sessions**: Studio sessions for albums or songs
- **Tours**: Series of related concerts
- **Award Shows**: Music industry recognition events
- **Conferences**: Music industry business events

### Duration Handling
Events can be:

- **Single-day events**: Same start and end date
- **Multi-day events**: Different start and end dates
- **Ongoing events**: Events without a defined end date
- **Historical events**: Events with varying date precision

### Description Levels
The system provides multiple description levels:

- **Name**: Short, identifying title
- **Short Description**: Brief summary for listings
- **Full Description**: Detailed information including lineup, venue, significance

## Related Entities

### Core Associations
- **Releases**: Albums, singles, or other releases associated with the event
- **Alternative Names**: Different names or titles for the same event

### Release Connections
Events connect to releases through various relationships:

- **Launch Events**: Album release parties or promotional events
- **Live Recordings**: Concerts that resulted in live albums
- **Festival Releases**: Compilation albums from festivals
- **Commemorative Releases**: Albums created to commemorate events

### Historical Tracking
All event-release associations are tracked through history tables, maintaining a complete audit trail.

## Usage Examples

### Concert Event
```
name: "Artist World Tour - City Name"
short_description: "Solo concert at Major Venue"
description: "Artist performs songs from their latest album plus classic hits at the 20,000-capacity Major Venue."
start_date: 2023-07-15
start_date_precision: Day
end_date: 2023-07-15
end_date_precision: Day

Related data:
- release_event: Links to live album recorded at this show
```

### Music Festival
```
name: "Summer Music Festival 2023"
short_description: "Three-day outdoor music festival"
description: "Annual festival featuring 50+ artists across multiple stages, showcasing rock, pop, and electronic music."
start_date: 2023-08-18
start_date_precision: Day
end_date: 2023-08-20
end_date_precision: Day

Related data:
- event_alternative_name: "SMF 2023", "Summer Fest"
- release_event: Festival compilation album
```

### Album Launch
```
name: "Album Title Release Party"
short_description: "Official album launch event"
description: "Intimate venue performance celebrating the release of 'Album Title' with acoustic versions of new songs."
start_date: 2023-06-01
start_date_precision: Day
end_date: 2023-06-01
end_date_precision: Day

Related data:
- release_event: Links to the album being launched
```

### Historical Event
```
name: "Legendary Concert 1969"
short_description: "Historic rock festival"
description: "Groundbreaking festival that defined a generation of music."
start_date: 1969-08-01
start_date_precision: Month
end_date: 1969-08-01
end_date_precision: Month

Related data:
- release_event: Multiple live albums and documentaries
- event_alternative_name: Various historical names
```

### Recording Session
```
name: "Abbey Road Sessions"
short_description: "Album recording sessions"
description: "Recording sessions for the landmark album at the famous London studio."
start_date: 1969-02-01
start_date_precision: Month
end_date: 1969-08-01
end_date_precision: Month

Related data:
- release_event: Links to the resulting studio album
```

## Alternative Names

Events can have multiple names through the `event_alternative_name` entity:

### Name Variations
- **Abbreviations**: "SMF" for "Summer Music Festival"
- **Colloquial Names**: "The Big Show" for formal event names
- **Historical Names**: Names that changed over time
- **Localized Names**: Names in different languages

### Marketing Names
- **Promotional Titles**: Marketing-specific event names
- **Sponsor Names**: Names including sponsor information
- **Series Names**: Names for recurring events

## Event-Release Relationships

Events connect to releases in various ways:

### Live Recordings
- **Concert Albums**: Full concerts released as albums
- **Festival Compilations**: Multi-artist releases from festivals
- **Bootlegs**: Unofficial recordings from events

### Promotional Releases
- **Launch Events**: Events specifically for album releases
- **Promotional Singles**: Releases tied to specific events
- **Commemorative Releases**: Albums created to remember events

### Documentary Releases
- **Concert Films**: Video releases of performances
- **Behind-the-Scenes**: Documentary releases about events
- **Anniversary Releases**: Releases commemorating historical events

## Date Precision and Historical Context

The system accommodates varying levels of historical documentation:

### Modern Events
- **Exact Dates**: Precise scheduling for contemporary events
- **Time Information**: Could be extended to include specific times
- **Venue Information**: Could be linked to venue entities

### Historical Events
- **Approximate Dates**: Month or year precision for older events
- **Uncertain Information**: Handling incomplete historical records
- **Research Updates**: Ability to improve precision as information becomes available

## Integration with Music Catalog

Events provide context for the music catalog:

### Performance Context
- **Live Versions**: Understanding where live recordings originated
- **Historical Significance**: Context for important musical moments
- **Artist Development**: Tracking career milestones and performances

### Release Context
- **Launch Events**: Understanding the promotional context of releases
- **Recording Context**: Where and when music was created
- **Cultural Context**: The broader cultural moment surrounding releases

### Fan Experience
- **Concert History**: Tracking performances and setlists
- **Festival Lineups**: Understanding multi-artist events
- **Memorable Moments**: Preserving significant musical events

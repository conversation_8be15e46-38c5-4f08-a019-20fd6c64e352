# Song Entity

The `song` entity represents individual musical compositions or tracks. Songs are the fundamental unit of musical content in the database.

## Entity Structure

```rust
pub struct Model {
    pub id: i32,        // Primary key
    pub title: String,  // Song title (Text type)
}
```

## Field Descriptions

### Core Fields
- **id**: Unique identifier for the song
- **title**: The primary title of the song (stored as Text for unlimited length)

## Design Philosophy

The song entity follows a minimalist core design, with most metadata stored in related entities. This approach provides:

- **Flexibility**: Additional attributes can be added without changing the core structure
- **Internationalization**: Multiple titles and metadata in different languages
- **History Tracking**: All changes are tracked in related history tables
- **Extensibility**: New types of song metadata can be added as needed

## Relationships

### Direct Relationships (has_many)
- **release_track**: Tracks on releases that contain this song
- **release_track_history**: Historical track associations
- **song_artist**: Artists associated with this song
- **song_credit**: Credits for this song (writers, composers, producers)
- **song_language**: Languages in which this song is performed
- **song_localized_title**: Titles in different languages
- **song_lyrics_history**: Historical changes to lyrics

### Direct Relationships (has_one)
- **song_lyrics**: Current lyrics for this song

### Indirect Relationships (via junction tables)
- **artists**: Through `song_artist` junction
- **releases**: Through `release_track` junction
- **languages**: Through `song_language` junction
- **credit_roles**: Through `song_credit` junction

## Business Logic

### Song Identity
Songs represent the abstract musical composition, separate from specific recordings or performances:

- A song can appear on multiple releases
- Different artists can perform the same song
- Songs can have multiple versions (covers, remixes, live versions)
- The same song can have different titles in different languages or regions

### Metadata Organization
Most song metadata is stored in related entities:

- **Titles**: Localized and alternative titles
- **Artists**: Performers, composers, writers
- **Credits**: Detailed contribution information
- **Languages**: Languages of performance or lyrics
- **Lyrics**: Full lyrical content with history tracking

### Version Handling
The system handles different versions of songs through:

- **Release Tracks**: Specific recordings on releases
- **Artist Associations**: Different performers of the same song
- **Credits**: Different contributors to different versions

## Related Entities

### Core Associations
- **Artists**: Composers, performers, writers, and other contributors
- **Releases**: Albums, singles, and other releases containing the song
- **Credits**: Detailed role-based contribution tracking

### Content & Metadata
- **Lyrics**: Full lyrical content with versioning
- **Languages**: Languages of performance and lyrics
- **Localized Titles**: Song titles in multiple languages and scripts

### Release Integration
- **Release Tracks**: Specific instances of the song on releases
  - Track numbers and positioning
  - Release-specific metadata
  - Disc and side organization

## Usage Examples

### Simple Song
```
id: 1
title: "Example Song"

Related data:
- song_artist: Links to composer and performer
- song_lyrics: Contains the song's lyrics
- release_track: Appears on "Example Album"
```

### Multi-language Song
```
id: 2
title: "Original Title"

Related data:
- song_localized_title: 
  - English: "English Title"
  - Spanish: "Título en Español"
  - Japanese: "日本語のタイトル"
- song_language: English, Spanish
```

### Cover Song
```
Original:
id: 3
title: "Classic Song"
- song_artist: Original Artist (composer)
- release_track: Original Album (1970)

Cover Version:
id: 3 (same song)
title: "Classic Song"
- song_artist: Cover Artist (performer)
- song_credit: Original Artist (composer), Cover Artist (performer)
- release_track: Cover Album (2023)
```

### Collaborative Song
```
id: 4
title: "Collaboration"

Related data:
- song_artist: 
  - Artist A (performer)
  - Artist B (performer)
- song_credit:
  - Artist A (composer, lyricist)
  - Artist B (composer)
  - Producer X (producer)
  - Engineer Y (recording engineer)
```

## Lyrics Management

Songs can have lyrics managed through the `song_lyrics` entity:

- **Current Lyrics**: The most up-to-date version
- **History Tracking**: All previous versions and changes
- **Multi-language Support**: Lyrics in different languages
- **Versioning**: Track changes over time with full audit trail

## Track vs Song Distinction

Important distinction in the system:

- **Song**: The abstract musical composition
- **Release Track**: A specific recording/performance of a song on a release

This allows:
- Multiple recordings of the same song
- Different arrangements or versions
- Proper crediting for different performances
- Accurate release tracking

## Integration with Releases

Songs connect to releases through the `release_track` entity, which provides:

- **Track Positioning**: Track numbers, disc numbers
- **Release-Specific Data**: Track-specific credits, notes
- **Version Information**: Different mixes, edits, or arrangements
- **Artist Associations**: Track-level artist credits

This design supports complex scenarios like:
- Compilation albums with tracks from different sources
- Multi-disc releases with complex organization
- Special editions with bonus tracks
- Live albums with different performances of the same songs

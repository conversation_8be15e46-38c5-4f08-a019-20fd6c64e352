# Release Entity

The `release` entity represents musical releases such as albums, EPs, singles, compilations, and other collections of music.

## Entity Structure

```rust
pub struct Model {
    pub id: i32,                                    // Primary key
    pub title: String,                              // Release title (Text type)
    pub release_type: ReleaseType,                  // Album, EP, Single, etc.
    pub release_date: Option<Date>,                 // Official release date
    pub release_date_precision: DatePrecision,     // Date precision level
    pub recording_date_start: Option<Date>,         // Recording period start
    pub recording_date_start_precision: DatePrecision, // Start date precision
    pub recording_date_end: Option<Date>,           // Recording period end
    pub recording_date_end_precision: DatePrecision,   // End date precision
}
```

## Field Descriptions

### Core Fields
- **id**: Unique identifier for the release
- **title**: The primary title of the release (stored as Text for unlimited length)
- **release_type**: Categorizes the type of release

### Release Types
- **Album**: Full-length studio album
- **EP**: Extended Play (typically 3-7 tracks)
- **Single**: Single track release (may include B-sides)
- **Compilation**: Collection of previously released tracks
- **Demo**: Demonstration recording
- **Other**: Any other type of release

### Date Information
- **release_date**: When the release was officially made available to the public
- **recording_date_start/end**: The period during which the release was recorded
- **Date precision levels**:
  - `Day`: Exact date known
  - `Month`: Month and year known
  - `Year`: Only year known

## Relationships

### Direct Relationships (has_many)
- **release_artist**: Artists associated with this release
- **release_catalog_number**: Catalog numbers from different labels
- **release_credit**: Credits for the release (producers, engineers, etc.)
- **release_event**: Events where this release was performed or launched
- **release_image**: Cover art and other images
- **release_image_queue**: Pending image approvals
- **release_localized_title**: Titles in different languages
- **release_track**: Individual tracks on this release

### Indirect Relationships (via junction tables)
- **artists**: Through `release_artist` junction
- **events**: Through `release_event` junction
- **images**: Through `release_image` junction
- **songs**: Through `release_track` junction
- **labels**: Through `release_catalog_number` junction

## Business Logic

### Release Types Hierarchy
The system distinguishes between different types of musical releases:

- **Albums**: Major releases, typically 8+ tracks, representing significant artistic statements
- **EPs**: Shorter releases, usually 3-7 tracks, often used for new material between albums
- **Singles**: Focus on one main track, may include remixes or B-sides
- **Compilations**: Collections of existing material, greatest hits, or themed collections
- **Demos**: Rough recordings, often unreleased or limited distribution
- **Other**: Covers edge cases like soundtracks, live albums, or experimental releases

### Date Tracking
The system tracks multiple date types:

1. **Release Date**: Official public availability
2. **Recording Dates**: When the music was actually created
   - Supports date ranges for releases recorded over extended periods
   - Useful for historical context and production timelines

### Multi-format Support
Through catalog numbers, releases can have multiple formats:
- Physical formats (CD, vinyl, cassette)
- Digital releases
- Different regional releases
- Reissues and remasters

## Related Entities

### Core Associations
- **Artists**: Main artists, featured artists, and contributors
- **Tracks**: Individual songs that make up the release
- **Labels**: Record labels that released or distributed the album
- **Events**: Launch events, concerts, or performances

### Metadata
- **Localized Titles**: Support for release titles in multiple languages
- **Images**: Cover art, back covers, liner notes, promotional images
- **Catalog Numbers**: Official release identifiers from labels
- **Credits**: Detailed production credits (producers, engineers, studios)

### History Tracking
Changes to release information are tracked through history tables, maintaining a complete audit trail.

## Usage Examples

### Studio Album
```
title: "The Great Album"
release_type: Album
release_date: 2023-06-15
release_date_precision: Day
recording_date_start: 2022-10-01
recording_date_start_precision: Month
recording_date_end: 2023-02-28
recording_date_end_precision: Day
```

### Single Release
```
title: "Hit Song"
release_type: Single
release_date: 2023-03-01
release_date_precision: Day
recording_date_start: 2023-01-15
recording_date_start_precision: Day
recording_date_end: 2023-01-15
recording_date_end_precision: Day
```

### Historical Release
```
title: "Classic Album"
release_type: Album
release_date: 1975-01-01
release_date_precision: Year
recording_date_start: 1974-01-01
recording_date_start_precision: Year
recording_date_end: 1974-01-01
recording_date_end_precision: Year
```

### Compilation
```
title: "Greatest Hits Collection"
release_type: Compilation
release_date: 2023-12-01
release_date_precision: Day
recording_date_start: 2010-01-01
recording_date_start_precision: Year
recording_date_end: 2022-01-01
recording_date_end_precision: Year
```

## Track Relationships

Releases contain tracks through the `release_track` entity, which provides:
- Track ordering and numbering
- Track-specific metadata
- Disc/side organization for multi-disc releases
- Individual track credits and information

This allows for complex release structures while maintaining the relationship between releases and the songs they contain.

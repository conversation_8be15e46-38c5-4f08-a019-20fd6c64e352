# Release (发行版本)

音乐发行版本，包括专辑、EP、单曲、合辑等音乐作品集合。

## 基本信息

| 字段                           | 类型 | 说明         |
| ------------------------------ | ---- | ------------ |
| id                             | 整数 | 唯一标识符   |
| title                          | 文本 | 发行版本标题 |
| release_type                   | 枚举 | 发行类型     |
| release_date                   | 日期 | 正式发行日期 |
| release_date_precision         | 枚举 | 发行日期精度 |
| recording_date_start           | 日期 | 录制开始日期 |
| recording_date_start_precision | 枚举 | 开始日期精度 |
| recording_date_end             | 日期 | 录制结束日期 |
| recording_date_end_precision   | 枚举 | 结束日期精度 |

## 发行类型

- **Album** - 完整专辑（通常 8 首以上）
- **EP** - 迷你专辑（通常 3-7 首）
- **Single** - 单曲发行（可能包含 B 面歌曲）
- **Compilation** - 精选集或合辑
- **Demo** - 样本录音
- **Other** - 其他类型

## 日期精度

- **Day** - 精确到日
- **Month** - 精确到月
- **Year** - 精确到年

## 关联关系

### 直接关联

- **release_artist** - 发行版本关联的艺人
- **release_catalog_number** - 不同厂牌的目录编号
- **release_credit** - 发行版本制作人员（制作人、工程师等）
- **release_event** - 相关活动（发行会、演出等）
- **release_image** - 封面和其他图片
- **release_image_queue** - 待审核的图片
- **release_localized_title** - 多语言标题
- **release_track** - 发行版本包含的曲目

### 间接关联

- **艺人** - 通过 release_artist 关联
- **events**: Through `release_event` junction
- **images**: Through `release_image` junction
- **songs**: Through `release_track` junction
- **labels**: Through `release_catalog_number` junction

## Business Logic

### Release Types Hierarchy

The system distinguishes between different types of musical releases:

- **Albums**: Major releases, typically 8+ tracks, representing significant artistic statements
- **EPs**: Shorter releases, usually 3-7 tracks, often used for new material between albums
- **Singles**: Focus on one main track, may include remixes or B-sides
- **Compilations**: Collections of existing material, greatest hits, or themed collections
- **Demos**: Rough recordings, often unreleased or limited distribution
- **Other**: Covers edge cases like soundtracks, live albums, or experimental releases

### Date Tracking

The system tracks multiple date types:

1. **Release Date**: Official public availability
2. **Recording Dates**: When the music was actually created
   - Supports date ranges for releases recorded over extended periods
   - Useful for historical context and production timelines

### Multi-format Support

Through catalog numbers, releases can have multiple formats:

- Physical formats (CD, vinyl, cassette)
- Digital releases
- Different regional releases
- Reissues and remasters

## Related Entities

### Core Associations

- **Artists**: Main artists, featured artists, and contributors
- **Tracks**: Individual songs that make up the release
- **Labels**: Record labels that released or distributed the album
- **Events**: Launch events, concerts, or performances

### Metadata

- **Localized Titles**: Support for release titles in multiple languages
- **Images**: Cover art, back covers, liner notes, promotional images
- **Catalog Numbers**: Official release identifiers from labels
- **Credits**: Detailed production credits (producers, engineers, studios)

### History Tracking

Changes to release information are tracked through history tables, maintaining a complete audit trail.

## Usage Examples

### Studio Album

```
title: "The Great Album"
release_type: Album
release_date: 2023-06-15
release_date_precision: Day
recording_date_start: 2022-10-01
recording_date_start_precision: Month
recording_date_end: 2023-02-28
recording_date_end_precision: Day
```

### Single Release

```
title: "Hit Song"
release_type: Single
release_date: 2023-03-01
release_date_precision: Day
recording_date_start: 2023-01-15
recording_date_start_precision: Day
recording_date_end: 2023-01-15
recording_date_end_precision: Day
```

### Historical Release

```
title: "Classic Album"
release_type: Album
release_date: 1975-01-01
release_date_precision: Year
recording_date_start: 1974-01-01
recording_date_start_precision: Year
recording_date_end: 1974-01-01
recording_date_end_precision: Year
```

### Compilation

```
title: "Greatest Hits Collection"
release_type: Compilation
release_date: 2023-12-01
release_date_precision: Day
recording_date_start: 2010-01-01
recording_date_start_precision: Year
recording_date_end: 2022-01-01
recording_date_end_precision: Year
```

## Track Relationships

Releases contain tracks through the `release_track` entity, which provides:

- Track ordering and numbering
- Track-specific metadata
- Disc/side organization for multi-disc releases
- Individual track credits and information

This allows for complex release structures while maintaining the relationship between releases and the songs they contain.

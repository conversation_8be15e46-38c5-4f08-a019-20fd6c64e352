CREATE
OR REPLACE FUNCTION update_updated_at () RETURNS TRIGGER AS
$$
BEGIN
NEW.updated_at = NOW();

RETURN NEW;

END;

$$
LANGUAGE plpgsql;

-- <PERSON>reate enum type "ArtistType"
CREATE TYPE "public"."ArtistType" AS ENUM('Solo', 'Multiple', 'Unknown');

-- Create enum type "DatePrecision"
CREATE TYPE "public"."DatePrecision" AS ENUM('Day', 'Month', 'Year');

-- Create enum type "JoinYearType"
CREATE TYPE "public"."JoinYearType" AS ENUM('FoundingMember', 'Specific');

-- Create enum type "LeaveYearType"
CREATE TYPE "public"."LeaveYearType" AS ENUM('Unknown', 'Specific');

-- Create enum type "CommentTarget"
CREATE TYPE "public"."CommentTarget" AS ENUM('Correction');

-- <PERSON>reate enum type "CommentState"
CREATE TYPE "public"."CommentState" AS ENUM('Visable', 'InReview', 'Hidden', 'Deleted');

-- <PERSON><PERSON> enum type "CorrectionType"
CREATE TYPE "public"."CorrectionType" AS ENUM('Create', 'Update', 'Delete');

-- Create enum type "CorrectionStatus"
CREATE TYPE "public"."CorrectionStatus" AS ENUM('Pending', 'Approved', 'Rejected');

-- Create enum type "EntityType"
CREATE TYPE "public"."EntityType" AS ENUM(
  'Artist',
  'Label',
  'Release',
  'Song',
  'Tag',
  'Event'
);

-- Create enum type "CorrectionUserType"
CREATE TYPE "public"."CorrectionUserType" AS ENUM('Author', 'Co-Author', 'Reviewer', 'Approver');

-- Create enum type "AlternativeNameType"
CREATE TYPE "public"."AlternativeNameType" AS ENUM('Alias', 'Localization');

-- Create enum type "ReleaseType"
CREATE TYPE "public"."ReleaseType" AS ENUM(
  'Album',
  'EP',
  'Single',
  'Compilation',
  'Demo',
  'Other'
);

-- Create enum type "TagType"
CREATE TYPE "public"."TagType" AS ENUM('Descriptor', 'Genre', 'Movement', 'Scene');

-- Create enum type "TagRelationType"
CREATE TYPE "public"."TagRelationType" AS ENUM('Inherit', 'Derive');

-- Create "language" table
CREATE TABLE "public"."language" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "code" TEXT NOT NULL UNIQUE,
  "name" TEXT NOT NULL UNIQUE,
  PRIMARY KEY ("id")
);

-- Create "image" table
CREATE TABLE "public"."image" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "filename" TEXT NOT NULL,
  "directory" TEXT NOT NULL,
  "uploaded_by" INTEGER NOT NULL,
  "created_at" timestamptz NOT NULL DEFAULT NOW(),
  PRIMARY KEY ("id")
);

-- Create "user" table
CREATE TABLE "public"."user" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "name" TEXT NOT NULL UNIQUE,
  "password" TEXT NOT NULL,
  "avatar_id" INTEGER NULL REFERENCES "public"."image" ("id"),
  "last_login" timestamptz NOT NULL DEFAULT NOW(),
  PRIMARY KEY ("id")
);

ALTER TABLE
  "public"."image"
ADD
  CONSTRAINT "fk_image_uploaded_by" FOREIGN KEY ("uploaded_by") REFERENCES "public"."user" ("id");

-- Create "song" table
CREATE TABLE "public"."song" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "title" TEXT NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "artist" table
CREATE TABLE "public"."artist" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "name" TEXT NOT NULL,
  "artist_type" "public"."ArtistType" NOT NULL,
  "text_alias" TEXT [] NULL,
  "start_date" date NULL,
  "start_date_precision" "public"."DatePrecision" NULL,
  "end_date" date NULL,
  "end_date_precision" "public"."DatePrecision" NULL,
  PRIMARY KEY ("id")
);

-- Create "artist_history" table
CREATE TABLE "public"."artist_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "name" TEXT NOT NULL,
  "artist_type" "public"."ArtistType" NOT NULL,
  "text_alias" TEXT [] NULL,
  "start_date" date NULL,
  "start_date_precision" "public"."DatePrecision" NULL,
  "end_date" date NULL,
  "end_date_precision" "public"."DatePrecision" NULL,
  PRIMARY KEY ("id")
);

-- Create "artist_alias" table
CREATE TABLE "public"."artist_alias" (
  "first_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  "second_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  PRIMARY KEY ("first_id", "second_id"),
  CHECK (first_id < second_id)
);

-- Create index "idx_artist_alias_first_id" to table: "artist_alias"
CREATE INDEX "idx_artist_alias_first_id" ON "public"."artist_alias" ("first_id");

-- Create index "idx_artist_alias_second_id" to table: "artist_alias"
CREATE INDEX "idx_artist_alias_second_id" ON "public"."artist_alias" ("second_id");

-- Create "artist_alias_history" table
CREATE TABLE "public"."artist_alias_history" (
  "history_id" INTEGER NOT NULL REFERENCES "public"."artist_history" ("id"),
  "alias_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  PRIMARY KEY ("history_id", "alias_id")
);

-- Create index "idx_artist_alias_history_alias_id" to table: "artist_alias_history"
CREATE INDEX "idx_artist_alias_history_alias_id" ON "public"."artist_alias_history" ("alias_id");

-- Create index "idx_artist_alias_history_history_id" to table: "artist_alias_history"
CREATE INDEX "idx_artist_alias_history_history_id" ON "public"."artist_alias_history" ("history_id");

-- Set comment to table: "artist_alias_history"
COMMENT ON TABLE "public"."artist_alias_history" IS 'Track the historical changes in alias relationships between artists';

-- Create "artist_link" table
CREATE TABLE "public"."artist_link" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "artist_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  "url" TEXT NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "artist_link_history" table
CREATE TABLE "public"."artist_link_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "history_id" INTEGER NOT NULL REFERENCES "public"."artist_history" ("id"),
  "url" TEXT NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "artist_localized_name" table
CREATE TABLE "public"."artist_localized_name" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "artist_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  "language_id" INTEGER NOT NULL REFERENCES "public"."language" ("id"),
  "name" TEXT NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "artist_localized_name_history" table
CREATE TABLE "public"."artist_localized_name_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "history_id" INTEGER NOT NULL REFERENCES "public"."artist_history" ("id"),
  "language_id" INTEGER NOT NULL REFERENCES "public"."language" ("id"),
  "name" TEXT NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "comment" table
CREATE TABLE "public"."comment" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "content" TEXT NOT NULL,
  "state" "public"."CommentState" NOT NULL,
  "author_id" INTEGER NOT NULL REFERENCES "public"."user" ("id"),
  "target" "public"."CommentTarget" NOT NULL,
  "target_id" INTEGER NOT NULL,
  "parent_id" INTEGER NULL REFERENCES "public"."comment" ("id"),
  "created_at" timestamptz NOT NULL DEFAULT NOW(),
  "updated_at" timestamptz NOT NULL,
  PRIMARY KEY ("id")
);

CREATE TRIGGER update_comment_updated_at BEFORE
INSERT
  OR
UPDATE
  ON "public"."comment" FOR EACH ROW EXECUTE FUNCTION update_updated_at ();

-- Create "comment_revision" table
CREATE TABLE "public"."comment_revision" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "comment_id" INTEGER NOT NULL REFERENCES "public"."comment" ("id"),
  "content" TEXT NOT NULL,
  "created_at" timestamptz NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "correction" table
CREATE TABLE "public"."correction" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "status" "public"."CorrectionStatus" NOT NULL,
  "type" "public"."CorrectionType" NOT NULL,
  "entity_type" "public"."EntityType" NOT NULL,
  "entity_id" INTEGER NOT NULL,
  "created_at" timestamptz NOT NULL DEFAULT NOW(),
  "handled_at" timestamptz NULL,
  PRIMARY KEY ("id")
);

-- Create "correction_revision" table
CREATE TABLE "public"."correction_revision" (
  "correction_id" INTEGER NOT NULL REFERENCES "public"."correction" ("id"),
  "entity_history_id" INTEGER NOT NULL,
  "author_id" INTEGER NOT NULL REFERENCES "public"."user" ("id"),
  "description" TEXT NOT NULL,
  PRIMARY KEY ("correction_id", "entity_history_id")
);

-- Create "correction_user" table
CREATE TABLE "public"."correction_user" (
  "correction_id" INTEGER NOT NULL REFERENCES "public"."correction" ("id"),
  "user_id" INTEGER NOT NULL REFERENCES "public"."user" ("id"),
  "user_type" "public"."CorrectionUserType" NOT NULL,
  PRIMARY KEY ("correction_id", "user_id", "user_type")
);

-- Create "credit_role" table
CREATE TABLE "public"."credit_role" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "name" TEXT NOT NULL,
  "short_description" TEXT NOT NULL,
  "description" TEXT NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "credit_role_history" table
CREATE TABLE "public"."credit_role_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "name" TEXT NOT NULL,
  "short_description" TEXT NOT NULL,
  "description" TEXT NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "credit_role_inheritance" table
CREATE TABLE "public"."credit_role_inheritance" (
  "role_id" INTEGER NOT NULL REFERENCES "public"."credit_role" ("id"),
  "super_id" INTEGER NOT NULL REFERENCES "public"."credit_role" ("id"),
  PRIMARY KEY ("role_id", "super_id")
);

-- Create "credit_role_inheritance_history" table
CREATE TABLE "public"."credit_role_inheritance_history" (
  "history_id" INTEGER NOT NULL REFERENCES "public"."credit_role_history" ("id"),
  "super_id" INTEGER NOT NULL REFERENCES "public"."credit_role" ("id"),
  PRIMARY KEY ("history_id", "super_id")
);

-- Create "event" table
CREATE TABLE "public"."event" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "name" TEXT NOT NULL,
  "short_description" TEXT NOT NULL,
  "description" TEXT NOT NULL,
  "start_date" date NULL,
  "start_date_precision" "public"."DatePrecision" NOT NULL DEFAULT 'Day',
  "end_date" date NULL,
  "end_date_precision" "public"."DatePrecision" NOT NULL DEFAULT 'Day',
  PRIMARY KEY ("id"),
  CONSTRAINT "validate_date" CHECK (
    (end_date IS NOT NULL)
    AND (start_date IS NOT NULL)
    AND (end_date > start_date)
  )
);

-- Create "event_history" table
CREATE TABLE "public"."event_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "name" TEXT NOT NULL,
  "short_description" TEXT NOT NULL,
  "description" TEXT NOT NULL,
  "start_date" date NULL,
  "start_date_precision" "public"."DatePrecision" NOT NULL DEFAULT 'Day',
  "end_date" date NULL,
  "end_date_precision" "public"."DatePrecision" NOT NULL DEFAULT 'Day',
  PRIMARY KEY ("id"),
  CONSTRAINT "validate_date" CHECK (
    (end_date IS NOT NULL)
    AND (start_date IS NOT NULL)
    AND (end_date > start_date)
  )
);

-- Create "event_alternative_name" table
CREATE TABLE "public"."event_alternative_name" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "event_id" INTEGER NOT NULL REFERENCES "public"."event" ("id"),
  "name" TEXT NOT NULL,
  "type" "public"."AlternativeNameType" NOT NULL,
  "language_id" INTEGER NULL REFERENCES "public"."language" ("id"),
  PRIMARY KEY ("id"),
  CONSTRAINT "validate_language" CHECK (
    (
      (
        TYPE = 'Localization'::public."AlternativeNameType"
      )
      AND (language_id IS NOT NULL)
    )
    OR (
      (
        TYPE = 'Alias'::public."AlternativeNameType"
      )
      AND (language_id IS NULL)
    )
  )
);

-- Create "event_alternative_name_history" table
CREATE TABLE "public"."event_alternative_name_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "history_id" INTEGER NOT NULL REFERENCES "public"."event_history" ("id"),
  "name" TEXT NOT NULL,
  "type" "public"."AlternativeNameType" NOT NULL,
  "language_id" INTEGER NULL REFERENCES "public"."language" ("id"),
  PRIMARY KEY ("id"),
  CONSTRAINT "validate_language" CHECK (
    (
      (
        TYPE = 'Localization'::public."AlternativeNameType"
      )
      AND (language_id IS NOT NULL)
    )
    OR (
      (
        TYPE = 'Alias'::public."AlternativeNameType"
      )
      AND (language_id IS NULL)
    )
  )
);

-- Create "group_member" table
CREATE TABLE "public"."group_member" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "member_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  "group_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  PRIMARY KEY ("id")
);

-- Create "group_member_history" table
CREATE TABLE "public"."group_member_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "artist_history_id" INTEGER NOT NULL REFERENCES "public"."artist_history" ("id"),
  "related_artist_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  PRIMARY KEY ("id")
);

-- Create "group_member_join_leave" table
CREATE TABLE "public"."group_member_join_leave" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "group_member_id" INTEGER NOT NULL REFERENCES "public"."group_member" ("id"),
  "join_year" SMALLINT NULL,
  "join_year_type" "public"."JoinYearType" NOT NULL,
  "leave_year" SMALLINT NULL,
  "leave_year_type" "public"."LeaveYearType" NOT NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "join_year_type_check" CHECK (
    (
      join_year_type = 'FoundingMember'::public."JoinYearType"
    )
    AND (join_year IS NULL)
  ),
  CONSTRAINT "leave_year_type_check" CHECK (
    (
      leave_year_type = 'Unknown'::public."LeaveYearType"
    )
    AND (leave_year IS NULL)
  )
);

-- Create "group_member_join_leave_history" table
CREATE TABLE "public"."group_member_join_leave_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "group_member_history_id" INTEGER NOT NULL REFERENCES "public"."group_member_history" ("id"),
  "join_year" SMALLINT NULL,
  "join_year_type" "public"."JoinYearType" NOT NULL,
  "leave_year" SMALLINT NULL,
  "leave_year_type" "public"."LeaveYearType" NOT NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "join_year_type_check" CHECK (
    (
      join_year_type = 'FoundingMember'::public."JoinYearType"
    )
    AND (join_year IS NULL)
  ),
  CONSTRAINT "leave_year_type_check" CHECK (
    (
      leave_year_type = 'Unknown'::public."LeaveYearType"
    )
    AND (leave_year IS NULL)
  )
);

-- Create "group_member_role" table
CREATE TABLE "public"."group_member_role" (
  "group_member_id" INTEGER NOT NULL REFERENCES "public"."group_member" ("id"),
  "role_id" INTEGER NOT NULL REFERENCES "public"."credit_role" ("id"),
  PRIMARY KEY ("group_member_id", "role_id")
);

-- Create "group_member_role_history" table
CREATE TABLE "public"."group_member_role_history" (
  "group_member_history_id" INTEGER NOT NULL REFERENCES "public"."group_member_history" ("id"),
  "role_id" INTEGER NOT NULL REFERENCES "public"."credit_role" ("id"),
  PRIMARY KEY ("group_member_history_id", "role_id")
);

-- Create "label" table
CREATE TABLE "public"."label" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "name" TEXT NOT NULL,
  "founded_date" date NULL,
  "founded_date_precision" "public"."DatePrecision" NOT NULL DEFAULT 'Day',
  "dissolved_date" date NULL,
  "dissolved_date_precision" "public"."DatePrecision" NOT NULL DEFAULT 'Day',
  PRIMARY KEY ("id")
);

-- Create "label_history" table
CREATE TABLE "public"."label_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "name" TEXT NOT NULL,
  "founded_date" date NULL,
  "founded_date_precision" "public"."DatePrecision" NOT NULL DEFAULT 'Day',
  "dissolved_date" date NULL,
  "dissolved_date_precision" "public"."DatePrecision" NOT NULL DEFAULT 'Day',
  PRIMARY KEY ("id")
);

-- Create "label_founder" table
CREATE TABLE "public"."label_founder" (
  "label_id" INTEGER NOT NULL REFERENCES "label" ("id"),
  "artist_id" INTEGER NOT NULL REFERENCES "artist" ("id"),
  PRIMARY KEY ("label_id", "artist_id")
);

-- Create "label_founder_history" table
CREATE TABLE "public"."label_founder_history" (
  "history_id" INTEGER NOT NULL REFERENCES "label_history" ("id"),
  "artist_id" INTEGER NOT NULL REFERENCES "artist" ("id"),
  PRIMARY KEY ("history_id", "artist_id")
);

-- Create "label_localized_name" table
CREATE TABLE "public"."label_localized_name" (
  "label_id" INTEGER NOT NULL REFERENCES "label" ("id"),
  "language_id" INTEGER NOT NULL REFERENCES "language" ("id"),
  "name" TEXT NOT NULL,
  PRIMARY KEY ("label_id", "language_id", "name")
);

-- Create "label_localized_name_history" table
CREATE TABLE "public"."label_localized_name_history" (
  "history_id" INTEGER NOT NULL REFERENCES "label_history" ("id"),
  "language_id" INTEGER NOT NULL REFERENCES "language" ("id"),
  "name" TEXT NOT NULL,
  PRIMARY KEY ("history_id", "language_id", "name")
);

-- Set comment to column: "code" on table: "language"
COMMENT ON COLUMN "public"."language"."code" IS 'Language code of ISO 639-3';

-- Create "release" table
CREATE TABLE "public"."release" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "title" TEXT NOT NULL,
  "release_type" "public"."ReleaseType" NOT NULL,
  "release_date" date NULL,
  "release_date_precision" "public"."DatePrecision" NOT NULL DEFAULT 'Day',
  "recording_date_start" date NULL,
  "recording_date_start_precision" "public"."DatePrecision" NOT NULL DEFAULT 'Day',
  "recording_date_end" date NULL,
  "recording_date_end_precision" "public"."DatePrecision" NOT NULL DEFAULT 'Day',
  PRIMARY KEY ("id")
);

-- Create "release_history" table
CREATE TABLE "public"."release_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "title" TEXT NOT NULL,
  "release_type" "public"."ReleaseType" NOT NULL,
  "release_date" date NULL,
  "release_date_precision" "public"."DatePrecision" NOT NULL DEFAULT 'Day',
  "recording_date_start" date NULL,
  "recording_date_start_precision" "public"."DatePrecision" NOT NULL DEFAULT 'Day',
  "recording_date_end" date NULL,
  "recording_date_end_precision" "public"."DatePrecision" NOT NULL DEFAULT 'Day',
  PRIMARY KEY ("id")
);

-- Create "release_artist" table
CREATE TABLE "public"."release_artist" (
  "release_id" INTEGER NOT NULL REFERENCES "public"."release" ("id"),
  "artist_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  PRIMARY KEY ("release_id", "artist_id")
);

-- Create "release_artist_history" table
CREATE TABLE "public"."release_artist_history" (
  "history_id" INTEGER NOT NULL REFERENCES "public"."release_history" ("id"),
  "artist_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  PRIMARY KEY ("history_id", "artist_id")
);

-- Create "release_catalog_number" table
CREATE TABLE "public"."release_catalog_number" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "release_id" INTEGER NOT NULL REFERENCES "public"."release" ("id"),
  "catalog_number" TEXT NULL,
  "label_id" INTEGER NULL REFERENCES "public"."label" ("id"),
  PRIMARY KEY ("id")
);

-- Create "release_catalog_number_history" table
CREATE TABLE "public"."release_catalog_number_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "history_id" INTEGER NOT NULL REFERENCES "public"."release_history" ("id"),
  "catalog_number" TEXT NULL,
  "label_id" INTEGER NULL REFERENCES "public"."label" ("id"),
  PRIMARY KEY ("id")
);

-- Create "release_credit" table
CREATE TABLE "public"."release_credit" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "artist_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  "release_id" INTEGER NOT NULL REFERENCES "public"."release" ("id"),
  "role_id" INTEGER NOT NULL REFERENCES "public"."credit_role" ("id"),
  "on" SMALLINT [] NULL,
  PRIMARY KEY ("id")
);

-- Create "release_credit_history" table
CREATE TABLE "public"."release_credit_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "artist_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  "history_id" INTEGER NOT NULL REFERENCES "public"."release_history" ("id"),
  "role_id" INTEGER NOT NULL REFERENCES "public"."credit_role" ("id"),
  "on" SMALLINT [] NULL,
  PRIMARY KEY ("id")
);

-- Create "release_event" table
CREATE TABLE "public"."release_event" (
  "release_id" INTEGER NOT NULL REFERENCES "public"."release" ("id"),
  "event_id" INTEGER NOT NULL REFERENCES "public"."event" ("id"),
  PRIMARY KEY ("release_id", "event_id")
);

-- Create "release_event_history" table
CREATE TABLE "public"."release_event_history" (
  "history_id" INTEGER NOT NULL REFERENCES "public"."release_history" ("id"),
  "event_id" INTEGER NOT NULL REFERENCES "public"."event" ("id"),
  PRIMARY KEY ("history_id", "event_id")
);

-- Create "release_localized_title" table
CREATE TABLE "public"."release_localized_title" (
  "release_id" INTEGER NOT NULL REFERENCES "public"."release" ("id"),
  "language_id" INTEGER NOT NULL REFERENCES "public"."language" ("id"),
  "title" TEXT NOT NULL,
  PRIMARY KEY ("release_id", "language_id", "title")
);

-- Create "release_localized_title_history" table
CREATE TABLE "public"."release_localized_title_history" (
  "history_id" INTEGER NOT NULL REFERENCES "public"."release_history" ("id"),
  "language_id" INTEGER NOT NULL REFERENCES "public"."language" ("id"),
  "title" TEXT NOT NULL,
  PRIMARY KEY ("history_id", "language_id", "title")
);

-- Create "release_track" table
CREATE TABLE "public"."release_track" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "release_id" INTEGER NOT NULL REFERENCES "public"."release" ("id"),
  "song_id" INTEGER NOT NULL REFERENCES "public"."song" ("id"),
  "track_number" TEXT NULL,
  "display_title" TEXT NULL,
  "duration" INTERVAL NULL,
  PRIMARY KEY ("id")
);

-- Create index "idx_release_track_song_id" to table: "release_track"
CREATE INDEX "idx_release_track_song_id" ON "public"."release_track" ("song_id");

-- Create "release_track_artist" table
CREATE TABLE "public"."release_track_artist" (
  "track_id" INTEGER NOT NULL REFERENCES "public"."release_track" ("id"),
  "artist_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  PRIMARY KEY ("track_id", "artist_id")
);

-- Create "release_track_history" table
CREATE TABLE "public"."release_track_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "history_id" INTEGER NOT NULL REFERENCES "public"."release_history" ("id"),
  "song_id" INTEGER NOT NULL REFERENCES "public"."song" ("id"),
  "track_number" TEXT NULL,
  "display_title" TEXT NULL,
  "duration" INTERVAL NULL,
  PRIMARY KEY ("id")
);

-- Create "release_track_artist_history" table
CREATE TABLE "public"."release_track_artist_history" (
  "track_history_id" INTEGER NOT NULL REFERENCES "public"."release_track_history" ("id"),
  "artist_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  PRIMARY KEY ("track_history_id", "artist_id")
);

-- Create index "idx_release_track_history_song_id" to table: "release_track_history"
CREATE INDEX "idx_release_track_history_song_id" ON "public"."release_track_history" ("song_id");

-- Create "role" table
CREATE TABLE "public"."role" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "name" TEXT NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "song_history" table
CREATE TABLE "public"."song_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "title" TEXT NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "song_artist" table
CREATE TABLE "public"."song_artist" (
  "song_id" INTEGER NOT NULL REFERENCES "public"."song" ("id"),
  "artist_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  PRIMARY KEY ("song_id", "artist_id")
);

-- Create "song_artist_history" table
CREATE TABLE "public"."song_artist_history" (
  "history_id" INTEGER NOT NULL REFERENCES "public"."song_history" ("id"),
  "artist_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  PRIMARY KEY ("history_id", "artist_id")
);

-- Create "song_credit" table
CREATE TABLE "public"."song_credit" (
  "song_id" INTEGER NOT NULL REFERENCES "public"."song" ("id"),
  "artist_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  "role_id" INTEGER NOT NULL REFERENCES "public"."credit_role" ("id"),
  PRIMARY KEY ("artist_id", "song_id", "role_id")
);

-- Create "song_credit_history" table
CREATE TABLE "public"."song_credit_history" (
  "history_id" INTEGER NOT NULL REFERENCES "public"."song_history" ("id"),
  "artist_id" INTEGER NOT NULL REFERENCES "public"."artist" ("id"),
  "role_id" INTEGER NOT NULL REFERENCES "public"."credit_role" ("id"),
  PRIMARY KEY ("history_id", "artist_id", "role_id")
);

-- Create "song_language" table
CREATE TABLE "public"."song_language" (
  "song_id" INTEGER NOT NULL REFERENCES "public"."song" ("id"),
  "language_id" INTEGER NOT NULL REFERENCES "public"."language" ("id"),
  PRIMARY KEY ("song_id", "language_id")
);

-- Create "song_language_history" table
CREATE TABLE "public"."song_language_history" (
  "history_id" INTEGER NOT NULL REFERENCES "public"."song_history" ("id"),
  "language_id" INTEGER NOT NULL REFERENCES "public"."language" ("id"),
  PRIMARY KEY ("history_id", "language_id")
);

-- Create "song_localized_title" table
CREATE TABLE "public"."song_localized_title" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "song_id" INTEGER NOT NULL REFERENCES "public"."song" ("id"),
  "language_id" INTEGER NOT NULL REFERENCES "public"."language" ("id"),
  "title" TEXT NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "song_localized_title_history" table
CREATE TABLE "public"."song_localized_title_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "history_id" INTEGER NOT NULL REFERENCES "public"."song_history" ("id"),
  "language_id" INTEGER NOT NULL REFERENCES "public"."language" ("id"),
  "title" TEXT NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "song_relation" table
CREATE TABLE "public"."song_relation" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "first_id" INTEGER NOT NULL REFERENCES "public"."song" ("id"),
  "second_id" INTEGER NOT NULL REFERENCES "public"."song" ("id"),
  "relation_type" TEXT NOT NULL,
  "description" TEXT NOT NULL,
  PRIMARY KEY ("id"),
  CHECK (first_id < second_id)
);

-- Create "tag" table
CREATE TABLE "public"."tag" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "name" TEXT NOT NULL,
  "type" "public"."TagType" NOT NULL,
  "short_description" TEXT NOT NULL,
  "description" TEXT NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "tag_history" table
CREATE TABLE "public"."tag_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "name" TEXT NOT NULL,
  "type" "public"."TagType" NOT NULL,
  "short_description" TEXT NOT NULL,
  "description" TEXT NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "tag_alternative_name" table
CREATE TABLE "public"."tag_alternative_name" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "tag_id" INTEGER NOT NULL REFERENCES "public"."language" ("id"),
  "name" TEXT NOT NULL,
  "is_origin_language" BOOLEAN NOT NULL,
  "language_id" INTEGER NULL REFERENCES "public"."tag" ("id"),
  PRIMARY KEY ("id")
);

-- Create "tag_alternative_name_history" table
CREATE TABLE "public"."tag_alternative_name_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "history_id" INTEGER NOT NULL REFERENCES "public"."tag_history" ("id"),
  "name" TEXT NOT NULL,
  "is_origin_language" BOOLEAN NOT NULL,
  "language_id" INTEGER NULL REFERENCES "public"."language" ("id"),
  PRIMARY KEY ("id")
);

-- Create "tag_relation" table
CREATE TABLE "public"."tag_relation" (
  "tag_id" INTEGER NOT NULL REFERENCES "public"."tag" ("id"),
  "related_tag_id" INTEGER NOT NULL REFERENCES "public"."tag" ("id"),
  "type" "public"."TagRelationType" NOT NULL,
  PRIMARY KEY ("related_tag_id", "tag_id", "type"),
  CHECK (tag_id <> related_tag_id)
);

-- Create "tag_relation_history" table
CREATE TABLE "public"."tag_relation_history" (
  "history_id" INTEGER NOT NULL REFERENCES "public"."tag_history" ("id"),
  "related_tag_id" INTEGER NOT NULL REFERENCES "public"."tag" ("id"),
  "type" "public"."TagRelationType" NOT NULL,
  PRIMARY KEY ("related_tag_id", "history_id", "type")
);

-- Create "user_list" table
CREATE TABLE "public"."user_list" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "user_id" INTEGER NOT NULL REFERENCES "public"."user" ("id") ON UPDATE CASCADE,
  "name" TEXT NOT NULL,
  "description" TEXT NOT NULL,
  "is_public" BOOLEAN NOT NULL,
  PRIMARY KEY ("id")
);

-- Create "user_list_item" table
CREATE TABLE "public"."user_list_item" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "user_list_id" INTEGER NOT NULL REFERENCES "public"."user_list" ("id") ON UPDATE CASCADE ON DELETE CASCADE,
  "entity_id" INTEGER NULL,
  "entity_type" "public"."EntityType" NOT NULL,
  "description" TEXT NULL,
  PRIMARY KEY ("id")
);

-- Create "user_role" table
CREATE TABLE "public"."user_role" (
  "user_id" INTEGER NOT NULL REFERENCES "public"."user" ("id") ON UPDATE CASCADE,
  "role_id" INTEGER NOT NULL REFERENCES "public"."role" ("id") ON UPDATE CASCADE,
  PRIMARY KEY ("user_id", "role_id")
);

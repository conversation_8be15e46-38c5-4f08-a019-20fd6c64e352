-- Unified naming
ALTER TABLE
  "public"."image" RENAME COLUMN "created_at" TO "uploaded_at";

CREATE TYPE "public"."ImageQueueStatus" AS ENUM(
  'Pending',
  'Approved',
  'Rejected',
  'Cancelled',
  'Reverted'
);

CREATE TABLE "public"."image_queue" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "image_id" INTEGER NULL REFERENCES "public"."image" ("id") ON DELETE
  SET
    NULL,
    "status" "public"."ImageQueueStatus" NOT NULL DEFAULT 'Pending',
    "handled_at" TIMESTAMPTZ NULL,
    "handled_by" INTEGER NULL REFERENCES "public"."user" ("id"),
    "reverted_at" TIMESTAMPTZ NULL,
    "reverted_by" INTEGER NULL REFERENCES "public"."user" ("id"),
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "creaded_by" INTEGER NOT NULL REFERENCES "public"."user" ("id"),
    PRIMARY KEY ("id")
);

ALTER TABLE
  "public"."image_queue"
ADD
  CONSTRAINT "image_queue_image_id_null_check" CHECK (
    ("image_id" IS NULL) = (
      "status" = ANY (
        ARRAY ['Rejected', 'Cancelled']::"public"."ImageQueueStatus" []
      )
    )
  ),
ADD
  CONSTRAINT "image_queue_handled_by_null_check" CHECK (
    ("handled_by" IS NULL) = (
      "status" = 'Pending'::"public"."ImageQueueStatus"
    )
  );

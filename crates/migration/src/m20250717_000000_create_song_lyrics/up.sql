-- Add SongLyrics to EntityType enum
DO
$$
BEGIN
IF NOT EXISTS (
  SELECT
    1
  FROM
    pg_enum
    JOIN pg_type ON pg_type.oid = pg_enum.enumtypid
  WHERE
    pg_type.typname = 'EntityType'
    AND pg_enum.enumlabel = 'SongLyrics'
) THEN
ALTER TYPE "public"."EntityType"
ADD
  VALUE 'SongLyrics';

END IF;

END
$$
;

-- Create "song_lyrics" table
CREATE TABLE "public"."song_lyrics" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "song_id" INTEGER NOT NULL,
  "language_id" INTEGER NOT NULL,
  "content" TEXT NOT NULL,
  "is_main" BOOLEAN NOT NULL DEFAULT FALSE,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_song_lyrics_song_id" FOREIGN KEY ("song_id") REFERENCES "public"."song" ("id"),
  CONSTRAINT "fk_song_lyrics_language_id" FOREIGN KEY ("language_id") REFERENCES "public"."language" ("id"),
  CONSTRAINT "uniq_song_lyrics_song_and_language" UNIQUE (song_id, language_id)
);

-- Ensure at most one main lyrics per song
CREATE UNIQUE INDEX "uniq_song_lyrics_main_per_song" ON "public"."song_lyrics" ("song_id")
WHERE
  is_main = TRUE;

-- Create "song_lyrics_history" table
CREATE TABLE "public"."song_lyrics_history" (
  "id" INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "song_id" INTEGER NOT NULL,
  "language_id" INTEGER NOT NULL,
  "content" TEXT NOT NULL,
  "is_main" BOOLEAN NOT NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_song_lyrics_history_song_id" FOREIGN KEY ("song_id") REFERENCES "public"."song" ("id"),
  CONSTRAINT "fk_song_lyrics_history_language_id" FOREIGN KEY ("language_id") REFERENCES "public"."language" ("id")
);

-- Create index for efficient song lookup in history
CREATE INDEX "idx_song_lyrics_history_song_id" ON "public"."song_lyrics_history" ("song_id");

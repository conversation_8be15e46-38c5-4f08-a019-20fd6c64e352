//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    <PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "song")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text")]
    pub title: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::release_track::Entity")]
    ReleaseTrack,
    #[sea_orm(has_many = "super::release_track_history::Entity")]
    ReleaseTrackHistory,
    #[sea_orm(has_many = "super::song_artist::Entity")]
    SongArtist,
    #[sea_orm(has_many = "super::song_credit::Entity")]
    SongCredit,
    #[sea_orm(has_many = "super::song_language::Entity")]
    SongLanguage,
    #[sea_orm(has_many = "super::song_localized_title::Entity")]
    SongLocalizedTitle,
    #[sea_orm(has_one = "super::song_lyrics::Entity")]
    SongLyrics,
    #[sea_orm(has_many = "super::song_lyrics_history::Entity")]
    SongLyricsHistory,
}

impl Related<super::release_track::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseTrack.def()
    }
}

impl Related<super::release_track_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseTrackHistory.def()
    }
}

impl Related<super::song_artist::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongArtist.def()
    }
}

impl Related<super::song_credit::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongCredit.def()
    }
}

impl Related<super::song_language::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongLanguage.def()
    }
}

impl Related<super::song_localized_title::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongLocalizedTitle.def()
    }
}

impl Related<super::song_lyrics::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongLyrics.def()
    }
}

impl Related<super::song_lyrics_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongLyricsHistory.def()
    }
}

impl Related<super::artist::Entity> for Entity {
    fn to() -> RelationDef {
        super::song_artist::Relation::Artist.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::song_artist::Relation::Song.def().rev())
    }
}

impl Related<super::language::Entity> for Entity {
    fn to() -> RelationDef {
        super::song_language::Relation::Language.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::song_language::Relation::Song.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}

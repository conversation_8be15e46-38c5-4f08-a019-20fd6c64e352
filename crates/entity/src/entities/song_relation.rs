//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "song_relation")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub first_id: i32,
    pub second_id: i32,
    #[sea_orm(column_type = "Text")]
    pub relation_type: String,
    #[sea_orm(column_type = "Text")]
    pub description: String,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::song::Entity",
        from = "Column::FirstId",
        to = "super::song::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Song2,
    #[sea_orm(
        belongs_to = "super::song::Entity",
        from = "Column::SecondId",
        to = "super::song::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Song1,
}

impl ActiveModelBehavior for ActiveModel {}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    <PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "song_history")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text")]
    pub title: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bug, <PERSON>um<PERSON><PERSON>, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::song_artist_history::Entity")]
    SongArtistHistory,
    #[sea_orm(has_many = "super::song_credit_history::Entity")]
    SongCreditHistory,
    #[sea_orm(has_many = "super::song_language_history::Entity")]
    SongLanguageHistory,
    #[sea_orm(has_many = "super::song_localized_title_history::Entity")]
    SongLocalizedTitleHistory,
}

impl Related<super::song_artist_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongArtistHistory.def()
    }
}

impl Related<super::song_credit_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongCreditHistory.def()
    }
}

impl Related<super::song_language_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongLanguageHistory.def()
    }
}

impl Related<super::song_localized_title_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongLocalizedTitleHistory.def()
    }
}

impl Related<super::artist::Entity> for Entity {
    fn to() -> RelationDef {
        super::song_artist_history::Relation::Artist.def()
    }
    fn via() -> Option<RelationDef> {
        Some(
            super::song_artist_history::Relation::SongHistory
                .def()
                .rev(),
        )
    }
}

impl Related<super::language::Entity> for Entity {
    fn to() -> RelationDef {
        super::song_language_history::Relation::Language.def()
    }
    fn via() -> Option<RelationDef> {
        Some(
            super::song_language_history::Relation::SongHistory
                .def()
                .rev(),
        )
    }
}

impl ActiveModelBehavior for ActiveModel {}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::{ArtistType, DatePrecision};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "artist_history")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text")]
    pub name: String,
    pub artist_type: ArtistType,
    pub text_alias: Option<Vec<String>>,
    pub start_date: Option<Date>,
    pub start_date_precision: Option<DatePrecision>,
    pub end_date: Option<Date>,
    pub end_date_precision: Option<DatePrecision>,
    #[sea_orm(column_type = "Text", nullable)]
    pub current_location_country: Option<String>,
    #[sea_orm(column_type = "Text", nullable)]
    pub current_location_province: Option<String>,
    #[sea_orm(column_type = "Text", nullable)]
    pub current_location_city: Option<String>,
    #[sea_orm(column_type = "Text", nullable)]
    pub start_location_country: Option<String>,
    #[sea_orm(column_type = "Text", nullable)]
    pub start_location_province: Option<String>,
    #[sea_orm(column_type = "Text", nullable)]
    pub start_location_city: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::artist_alias_history::Entity")]
    ArtistAliasHistory,
    #[sea_orm(has_many = "super::artist_link_history::Entity")]
    ArtistLinkHistory,
    #[sea_orm(has_many = "super::artist_localized_name_history::Entity")]
    ArtistLocalizedNameHistory,
    #[sea_orm(has_many = "super::artist_membership_history::Entity")]
    ArtistMembershipHistory,
}

impl Related<super::artist_alias_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistAliasHistory.def()
    }
}

impl Related<super::artist_link_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistLinkHistory.def()
    }
}

impl Related<super::artist_localized_name_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistLocalizedNameHistory.def()
    }
}

impl Related<super::artist_membership_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistMembershipHistory.def()
    }
}

impl Related<super::artist::Entity> for Entity {
    fn to() -> RelationDef {
        super::artist_alias_history::Relation::Artist.def()
    }
    fn via() -> Option<RelationDef> {
        Some(
            super::artist_alias_history::Relation::ArtistHistory
                .def()
                .rev(),
        )
    }
}

impl ActiveModelBehavior for ActiveModel {}

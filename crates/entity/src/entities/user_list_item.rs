//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::EntityType;

#[derive(
    <PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "user_list_item")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub user_list_id: i32,
    pub entity_id: Option<i32>,
    pub entity_type: EntityType,
    #[sea_orm(column_type = "Text", nullable)]
    pub description: Option<String>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::user_list::Entity",
        from = "Column::UserListId",
        to = "super::user_list::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    UserList,
}

impl Related<super::user_list::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserList.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

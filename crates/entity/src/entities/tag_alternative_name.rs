//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "tag_alternative_name")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub tag_id: i32,
    #[sea_orm(column_type = "Text")]
    pub name: String,
    pub is_origin_language: bool,
    pub language_id: Option<i32>,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::language::Entity",
        from = "Column::TagId",
        to = "super::language::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Language,
    #[sea_orm(
        belongs_to = "super::tag::Entity",
        from = "Column::LanguageId",
        to = "super::tag::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Tag,
}

impl Related<super::language::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Language.def()
    }
}

impl Related<super::tag::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Tag.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::{ArtistType, DatePrecision};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "artist")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text")]
    pub name: String,
    pub artist_type: ArtistType,
    pub text_alias: Option<Vec<String>>,
    pub start_date: Option<Date>,
    pub start_date_precision: Option<DatePrecision>,
    pub end_date: Option<Date>,
    pub end_date_precision: Option<DatePrecision>,
    #[sea_orm(column_type = "Text", nullable)]
    pub current_location_country: Option<String>,
    #[sea_orm(column_type = "Text", nullable)]
    pub current_location_province: Option<String>,
    #[sea_orm(column_type = "Text", nullable)]
    pub current_location_city: Option<String>,
    #[sea_orm(column_type = "Text", nullable)]
    pub start_location_country: Option<String>,
    #[sea_orm(column_type = "Text", nullable)]
    pub start_location_province: Option<String>,
    #[sea_orm(column_type = "Text", nullable)]
    pub start_location_city: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::artist_alias_history::Entity")]
    ArtistAliasHistory,
    #[sea_orm(has_many = "super::artist_image::Entity")]
    ArtistImage,
    #[sea_orm(has_many = "super::artist_image_queue::Entity")]
    ArtistImageQueue,
    #[sea_orm(has_many = "super::artist_link::Entity")]
    ArtistLink,
    #[sea_orm(has_many = "super::artist_localized_name::Entity")]
    ArtistLocalizedName,
    #[sea_orm(has_many = "super::artist_membership_history::Entity")]
    ArtistMembershipHistory,
    #[sea_orm(has_many = "super::label_founder::Entity")]
    LabelFounder,
    #[sea_orm(has_many = "super::label_founder_history::Entity")]
    LabelFounderHistory,
    #[sea_orm(has_many = "super::release_artist::Entity")]
    ReleaseArtist,
    #[sea_orm(has_many = "super::release_artist_history::Entity")]
    ReleaseArtistHistory,
    #[sea_orm(has_many = "super::release_credit::Entity")]
    ReleaseCredit,
    #[sea_orm(has_many = "super::release_credit_history::Entity")]
    ReleaseCreditHistory,
    #[sea_orm(has_many = "super::release_track_artist::Entity")]
    ReleaseTrackArtist,
    #[sea_orm(has_many = "super::release_track_artist_history::Entity")]
    ReleaseTrackArtistHistory,
    #[sea_orm(has_many = "super::song_artist::Entity")]
    SongArtist,
    #[sea_orm(has_many = "super::song_artist_history::Entity")]
    SongArtistHistory,
    #[sea_orm(has_many = "super::song_credit::Entity")]
    SongCredit,
    #[sea_orm(has_many = "super::song_credit_history::Entity")]
    SongCreditHistory,
}

impl Related<super::artist_alias_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistAliasHistory.def()
    }
}

impl Related<super::artist_image::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistImage.def()
    }
}

impl Related<super::artist_image_queue::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistImageQueue.def()
    }
}

impl Related<super::artist_link::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistLink.def()
    }
}

impl Related<super::artist_localized_name::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistLocalizedName.def()
    }
}

impl Related<super::artist_membership_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistMembershipHistory.def()
    }
}

impl Related<super::label_founder::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::LabelFounder.def()
    }
}

impl Related<super::label_founder_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::LabelFounderHistory.def()
    }
}

impl Related<super::release_artist::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseArtist.def()
    }
}

impl Related<super::release_artist_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseArtistHistory.def()
    }
}

impl Related<super::release_credit::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseCredit.def()
    }
}

impl Related<super::release_credit_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseCreditHistory.def()
    }
}

impl Related<super::release_track_artist::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseTrackArtist.def()
    }
}

impl Related<super::release_track_artist_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseTrackArtistHistory.def()
    }
}

impl Related<super::song_artist::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongArtist.def()
    }
}

impl Related<super::song_artist_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongArtistHistory.def()
    }
}

impl Related<super::song_credit::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongCredit.def()
    }
}

impl Related<super::song_credit_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongCreditHistory.def()
    }
}

impl Related<super::artist_history::Entity> for Entity {
    fn to() -> RelationDef {
        super::artist_alias_history::Relation::ArtistHistory.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::artist_alias_history::Relation::Artist.def().rev())
    }
}

impl Related<super::image::Entity> for Entity {
    fn to() -> RelationDef {
        super::artist_image::Relation::Image.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::artist_image::Relation::Artist.def().rev())
    }
}

impl Related<super::image_queue::Entity> for Entity {
    fn to() -> RelationDef {
        super::artist_image_queue::Relation::ImageQueue.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::artist_image_queue::Relation::Artist.def().rev())
    }
}

impl Related<super::label::Entity> for Entity {
    fn to() -> RelationDef {
        super::label_founder::Relation::Label.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::label_founder::Relation::Artist.def().rev())
    }
}

impl Related<super::label_history::Entity> for Entity {
    fn to() -> RelationDef {
        super::label_founder_history::Relation::LabelHistory.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::label_founder_history::Relation::Artist.def().rev())
    }
}

impl Related<super::release::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_artist::Relation::Release.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::release_artist::Relation::Artist.def().rev())
    }
}

impl Related<super::release_history::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_artist_history::Relation::ReleaseHistory.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::release_artist_history::Relation::Artist.def().rev())
    }
}

impl Related<super::release_track::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_track_artist::Relation::ReleaseTrack.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::release_track_artist::Relation::Artist.def().rev())
    }
}

impl Related<super::release_track_history::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_track_artist_history::Relation::ReleaseTrackHistory.def()
    }
    fn via() -> Option<RelationDef> {
        Some(
            super::release_track_artist_history::Relation::Artist
                .def()
                .rev(),
        )
    }
}

impl Related<super::song::Entity> for Entity {
    fn to() -> RelationDef {
        super::song_artist::Relation::Song.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::song_artist::Relation::Artist.def().rev())
    }
}

impl Related<super::song_history::Entity> for Entity {
    fn to() -> RelationDef {
        super::song_artist_history::Relation::SongHistory.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::song_artist_history::Relation::Artist.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}

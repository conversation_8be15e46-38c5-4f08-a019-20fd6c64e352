//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    <PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "artist_membership_history")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub history_id: i32,
    pub artist_id: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::artist::Entity",
        from = "Column::ArtistId",
        to = "super::artist::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Artist,
    #[sea_orm(
        belongs_to = "super::artist_history::Entity",
        from = "Column::HistoryId",
        to = "super::artist_history::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    ArtistHistory,
    #[sea_orm(has_many = "super::artist_membership_role_history::Entity")]
    ArtistMembershipRoleHistory,
    #[sea_orm(has_many = "super::artist_membership_tenure_history::Entity")]
    ArtistMembershipTenureHistory,
}

impl Related<super::artist::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Artist.def()
    }
}

impl Related<super::artist_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistHistory.def()
    }
}

impl Related<super::artist_membership_role_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistMembershipRoleHistory.def()
    }
}

impl Related<super::artist_membership_tenure_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistMembershipTenureHistory.def()
    }
}

impl Related<super::credit_role::Entity> for Entity {
    fn to() -> RelationDef {
        super::artist_membership_role_history::Relation::CreditRole.def()
    }
    fn via() -> Option<RelationDef> {
        Some (super :: artist_membership_role_history :: Relation :: ArtistMembershipHistory . def () . rev ())
    }
}

impl ActiveModelBehavior for ActiveModel {}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "credit_role_inheritance")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub role_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub super_id: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::credit_role::Entity",
        from = "Column::RoleId",
        to = "super::credit_role::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    CreditRole2,
    #[sea_orm(
        belongs_to = "super::credit_role::Entity",
        from = "Column::SuperId",
        to = "super::credit_role::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    CreditRole1,
}

impl ActiveModelBehavior for ActiveModel {}

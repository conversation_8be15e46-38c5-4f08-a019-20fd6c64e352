//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::ImageQueueStatus;

#[derive(
    <PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "image_queue")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub image_id: Option<i32>,
    pub status: ImageQueueStatus,
    pub handled_at: Option<DateTimeWithTimeZone>,
    pub handled_by: Option<i32>,
    pub reverted_at: Option<DateTimeWithTimeZone>,
    pub reverted_by: Option<i32>,
    pub created_at: DateTimeWithTimeZone,
    pub creaded_by: i32,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::artist_image_queue::Entity")]
    ArtistImageQueue,
    #[sea_orm(
        belongs_to = "super::image::Entity",
        from = "Column::ImageId",
        to = "super::image::Column::Id",
        on_update = "NoAction",
        on_delete = "SetNull"
    )]
    Image,
    #[sea_orm(has_many = "super::release_image_queue::Entity")]
    ReleaseImageQueue,
    #[sea_orm(
        belongs_to = "super::user::Entity",
        from = "Column::CreadedBy",
        to = "super::user::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    User3,
    #[sea_orm(
        belongs_to = "super::user::Entity",
        from = "Column::HandledBy",
        to = "super::user::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    User2,
    #[sea_orm(
        belongs_to = "super::user::Entity",
        from = "Column::RevertedBy",
        to = "super::user::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    User1,
}

impl Related<super::artist_image_queue::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistImageQueue.def()
    }
}

impl Related<super::image::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Image.def()
    }
}

impl Related<super::release_image_queue::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseImageQueue.def()
    }
}

impl Related<super::artist::Entity> for Entity {
    fn to() -> RelationDef {
        super::artist_image_queue::Relation::Artist.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::artist_image_queue::Relation::ImageQueue.def().rev())
    }
}

impl Related<super::release::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_image_queue::Relation::Release.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::release_image_queue::Relation::ImageQueue.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}

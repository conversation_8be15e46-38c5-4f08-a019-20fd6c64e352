//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::ReleaseImageType;

#[derive(
    <PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "release_image_queue")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub release_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub queue_id: i32,
    pub r#type: ReleaseImageType,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::image_queue::Entity",
        from = "Column::QueueId",
        to = "super::image_queue::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    ImageQueue,
    #[sea_orm(
        belongs_to = "super::release::Entity",
        from = "Column::ReleaseId",
        to = "super::release::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Release,
}

impl Related<super::image_queue::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ImageQueue.def()
    }
}

impl Related<super::release::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Release.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

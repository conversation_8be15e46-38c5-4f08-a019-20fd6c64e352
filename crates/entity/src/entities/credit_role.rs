//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    <PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "credit_role")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text")]
    pub name: String,
    #[sea_orm(column_type = "Text")]
    pub short_description: String,
    #[sea_orm(column_type = "Text")]
    pub description: String,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::artist_membership_role::Entity")]
    ArtistMembershipRole,
    #[sea_orm(has_many = "super::artist_membership_role_history::Entity")]
    ArtistMembershipRoleHistory,
    #[sea_orm(has_many = "super::credit_role_inheritance_history::Entity")]
    CreditRoleInheritanceHistory,
    #[sea_orm(has_many = "super::release_credit::Entity")]
    ReleaseCredit,
    #[sea_orm(has_many = "super::release_credit_history::Entity")]
    ReleaseCreditHistory,
    #[sea_orm(has_many = "super::song_credit::Entity")]
    SongCredit,
    #[sea_orm(has_many = "super::song_credit_history::Entity")]
    SongCreditHistory,
}

impl Related<super::artist_membership_role::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistMembershipRole.def()
    }
}

impl Related<super::artist_membership_role_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistMembershipRoleHistory.def()
    }
}

impl Related<super::credit_role_inheritance_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::CreditRoleInheritanceHistory.def()
    }
}

impl Related<super::release_credit::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseCredit.def()
    }
}

impl Related<super::release_credit_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseCreditHistory.def()
    }
}

impl Related<super::song_credit::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongCredit.def()
    }
}

impl Related<super::song_credit_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongCreditHistory.def()
    }
}

impl Related<super::artist_membership::Entity> for Entity {
    fn to() -> RelationDef {
        super::artist_membership_role::Relation::ArtistMembership.def()
    }
    fn via() -> Option<RelationDef> {
        Some(
            super::artist_membership_role::Relation::CreditRole
                .def()
                .rev(),
        )
    }
}

impl Related<super::artist_membership_history::Entity> for Entity {
    fn to() -> RelationDef {
        super::artist_membership_role_history::Relation::ArtistMembershipHistory
            .def()
    }
    fn via() -> Option<RelationDef> {
        Some(
            super::artist_membership_role_history::Relation::CreditRole
                .def()
                .rev(),
        )
    }
}

impl Related<super::credit_role_history::Entity> for Entity {
    fn to() -> RelationDef {
        super::credit_role_inheritance_history::Relation::CreditRoleHistory
            .def()
    }
    fn via() -> Option<RelationDef> {
        Some(
            super::credit_role_inheritance_history::Relation::CreditRole
                .def()
                .rev(),
        )
    }
}

impl ActiveModelBehavior for ActiveModel {}

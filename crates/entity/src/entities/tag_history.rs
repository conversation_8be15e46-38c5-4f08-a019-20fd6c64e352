//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::TagType;

#[derive(
    <PERSON><PERSON>, <PERSON>bug, <PERSON>ialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "tag_history")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text")]
    pub name: String,
    pub r#type: TagType,
    #[sea_orm(column_type = "Text")]
    pub short_description: String,
    #[sea_orm(column_type = "Text")]
    pub description: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::tag_alternative_name_history::Entity")]
    TagAlternativeNameHistory,
    #[sea_orm(has_many = "super::tag_relation_history::Entity")]
    TagRelationHistory,
}

impl Related<super::tag_alternative_name_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TagAlternativeNameHistory.def()
    }
}

impl Related<super::tag_relation_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TagRelationHistory.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::DatePrecision;

#[derive(
    <PERSON><PERSON>, Debug, <PERSON>ialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "event")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text")]
    pub name: String,
    #[sea_orm(column_type = "Text")]
    pub short_description: String,
    #[sea_orm(column_type = "Text")]
    pub description: String,
    pub start_date: Option<Date>,
    pub start_date_precision: DatePrecision,
    pub end_date: Option<Date>,
    pub end_date_precision: DatePrecision,
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::event_alternative_name::Entity")]
    EventAlternativeName,
    #[sea_orm(has_many = "super::release_event::Entity")]
    ReleaseEvent,
    #[sea_orm(has_many = "super::release_event_history::Entity")]
    ReleaseEventHistory,
}

impl Related<super::event_alternative_name::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::EventAlternativeName.def()
    }
}

impl Related<super::release_event::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseEvent.def()
    }
}

impl Related<super::release_event_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseEventHistory.def()
    }
}

impl Related<super::release::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_event::Relation::Release.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::release_event::Relation::Event.def().rev())
    }
}

impl Related<super::release_history::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_event_history::Relation::ReleaseHistory.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::release_event_history::Relation::Event.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}

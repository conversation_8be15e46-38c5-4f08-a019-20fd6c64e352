//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

pub mod artist;
pub mod artist_alias;
pub mod artist_alias_history;
pub mod artist_history;
pub mod artist_image;
pub mod artist_image_queue;
pub mod artist_link;
pub mod artist_link_history;
pub mod artist_localized_name;
pub mod artist_localized_name_history;
pub mod artist_membership;
pub mod artist_membership_history;
pub mod artist_membership_role;
pub mod artist_membership_role_history;
pub mod artist_membership_tenure;
pub mod artist_membership_tenure_history;
pub mod comment;
pub mod comment_revision;
pub mod correction;
pub mod correction_revision;
pub mod correction_user;
pub mod credit_role;
pub mod credit_role_history;
pub mod credit_role_inheritance;
pub mod credit_role_inheritance_history;
pub mod event;
pub mod event_alternative_name;
pub mod event_alternative_name_history;
pub mod event_history;
pub mod image;
pub mod image_queue;
pub mod label;
pub mod label_founder;
pub mod label_founder_history;
pub mod label_history;
pub mod label_localized_name;
pub mod label_localized_name_history;
pub mod language;
pub mod release;
pub mod release_artist;
pub mod release_artist_history;
pub mod release_catalog_number;
pub mod release_catalog_number_history;
pub mod release_credit;
pub mod release_credit_history;
pub mod release_event;
pub mod release_event_history;
pub mod release_history;
pub mod release_image;
pub mod release_image_queue;
pub mod release_localized_title;
pub mod release_localized_title_history;
pub mod release_track;
pub mod release_track_artist;
pub mod release_track_artist_history;
pub mod release_track_history;
pub mod role;
pub mod sea_orm_active_enums;
pub mod song;
pub mod song_artist;
pub mod song_artist_history;
pub mod song_credit;
pub mod song_credit_history;
pub mod song_history;
pub mod song_language;
pub mod song_language_history;
pub mod song_localized_title;
pub mod song_localized_title_history;
pub mod song_lyrics;
pub mod song_lyrics_history;
pub mod song_relation;
pub mod tag;
pub mod tag_alternative_name;
pub mod tag_alternative_name_history;
pub mod tag_history;
pub mod tag_relation;
pub mod tag_relation_history;
pub mod user;
pub mod user_following;
pub mod user_list;
pub mod user_list_item;
pub mod user_role;

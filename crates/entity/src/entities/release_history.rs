//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::{DatePrecision, ReleaseType};

#[derive(
    <PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "release_history")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text")]
    pub title: String,
    pub release_type: ReleaseType,
    pub release_date: Option<Date>,
    pub release_date_precision: DatePrecision,
    pub recording_date_start: Option<Date>,
    pub recording_date_start_precision: DatePrecision,
    pub recording_date_end: Option<Date>,
    pub recording_date_end_precision: DatePrecision,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::release_artist_history::Entity")]
    ReleaseArtistHistory,
    #[sea_orm(has_many = "super::release_catalog_number_history::Entity")]
    ReleaseCatalogNumberHistory,
    #[sea_orm(has_many = "super::release_credit_history::Entity")]
    ReleaseCreditHistory,
    #[sea_orm(has_many = "super::release_event_history::Entity")]
    ReleaseEventHistory,
    #[sea_orm(has_many = "super::release_localized_title_history::Entity")]
    ReleaseLocalizedTitleHistory,
    #[sea_orm(has_many = "super::release_track_history::Entity")]
    ReleaseTrackHistory,
}

impl Related<super::release_artist_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseArtistHistory.def()
    }
}

impl Related<super::release_catalog_number_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseCatalogNumberHistory.def()
    }
}

impl Related<super::release_credit_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseCreditHistory.def()
    }
}

impl Related<super::release_event_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseEventHistory.def()
    }
}

impl Related<super::release_localized_title_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseLocalizedTitleHistory.def()
    }
}

impl Related<super::release_track_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseTrackHistory.def()
    }
}

impl Related<super::artist::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_artist_history::Relation::Artist.def()
    }
    fn via() -> Option<RelationDef> {
        Some(
            super::release_artist_history::Relation::ReleaseHistory
                .def()
                .rev(),
        )
    }
}

impl Related<super::event::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_event_history::Relation::Event.def()
    }
    fn via() -> Option<RelationDef> {
        Some(
            super::release_event_history::Relation::ReleaseHistory
                .def()
                .rev(),
        )
    }
}

impl ActiveModelBehavior for ActiveModel {}

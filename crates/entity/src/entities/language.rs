//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    <PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "language")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text", unique)]
    pub code: String,
    #[sea_orm(column_type = "Text", unique)]
    pub name: String,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::artist_localized_name::Entity")]
    ArtistLocalizedName,
    #[sea_orm(has_many = "super::artist_localized_name_history::Entity")]
    ArtistLocalizedNameHistory,
    #[sea_orm(has_many = "super::event_alternative_name::Entity")]
    EventAlternativeName,
    #[sea_orm(has_many = "super::event_alternative_name_history::Entity")]
    EventAlternativeNameHistory,
    #[sea_orm(has_many = "super::label_localized_name::Entity")]
    LabelLocalizedName,
    #[sea_orm(has_many = "super::label_localized_name_history::Entity")]
    LabelLocalizedNameHistory,
    #[sea_orm(has_many = "super::release_localized_title::Entity")]
    ReleaseLocalizedTitle,
    #[sea_orm(has_many = "super::release_localized_title_history::Entity")]
    ReleaseLocalizedTitleHistory,
    #[sea_orm(has_many = "super::song_language::Entity")]
    SongLanguage,
    #[sea_orm(has_many = "super::song_language_history::Entity")]
    SongLanguageHistory,
    #[sea_orm(has_many = "super::song_localized_title::Entity")]
    SongLocalizedTitle,
    #[sea_orm(has_many = "super::song_localized_title_history::Entity")]
    SongLocalizedTitleHistory,
    #[sea_orm(has_many = "super::song_lyrics::Entity")]
    SongLyrics,
    #[sea_orm(has_many = "super::song_lyrics_history::Entity")]
    SongLyricsHistory,
    #[sea_orm(has_many = "super::tag_alternative_name::Entity")]
    TagAlternativeName,
    #[sea_orm(has_many = "super::tag_alternative_name_history::Entity")]
    TagAlternativeNameHistory,
}

impl Related<super::artist_localized_name::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistLocalizedName.def()
    }
}

impl Related<super::artist_localized_name_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistLocalizedNameHistory.def()
    }
}

impl Related<super::event_alternative_name::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::EventAlternativeName.def()
    }
}

impl Related<super::event_alternative_name_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::EventAlternativeNameHistory.def()
    }
}

impl Related<super::label_localized_name::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::LabelLocalizedName.def()
    }
}

impl Related<super::label_localized_name_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::LabelLocalizedNameHistory.def()
    }
}

impl Related<super::release_localized_title::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseLocalizedTitle.def()
    }
}

impl Related<super::release_localized_title_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseLocalizedTitleHistory.def()
    }
}

impl Related<super::song_language::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongLanguage.def()
    }
}

impl Related<super::song_language_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongLanguageHistory.def()
    }
}

impl Related<super::song_localized_title::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongLocalizedTitle.def()
    }
}

impl Related<super::song_localized_title_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongLocalizedTitleHistory.def()
    }
}

impl Related<super::song_lyrics::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongLyrics.def()
    }
}

impl Related<super::song_lyrics_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SongLyricsHistory.def()
    }
}

impl Related<super::tag_alternative_name::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TagAlternativeName.def()
    }
}

impl Related<super::tag_alternative_name_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TagAlternativeNameHistory.def()
    }
}

impl Related<super::song::Entity> for Entity {
    fn to() -> RelationDef {
        super::song_language::Relation::Song.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::song_language::Relation::Language.def().rev())
    }
}

impl Related<super::song_history::Entity> for Entity {
    fn to() -> RelationDef {
        super::song_language_history::Relation::SongHistory.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::song_language_history::Relation::Language.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}

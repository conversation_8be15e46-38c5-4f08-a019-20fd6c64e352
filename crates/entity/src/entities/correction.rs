//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::{
    CorrectionStatus, CorrectionType, EntityType,
};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "correction")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub status: CorrectionStatus,
    pub r#type: CorrectionType,
    pub entity_type: EntityType,
    pub entity_id: i32,
    pub created_at: DateTimeWithTimeZone,
    pub handled_at: Option<DateTimeWithTimeZone>,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::correction_revision::Entity")]
    CorrectionRevision,
    #[sea_orm(has_many = "super::correction_user::Entity")]
    CorrectionUser,
}

impl Related<super::correction_revision::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::CorrectionRevision.def()
    }
}

impl Related<super::correction_user::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::CorrectionUser.def()
    }
}

impl Related<super::user::Entity> for Entity {
    fn to() -> RelationDef {
        super::correction_revision::Relation::User.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::correction_revision::Relation::Correction.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::TagRelationType;

#[derive(
    <PERSON>lone, <PERSON>bug, <PERSON>ialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "tag_relation_history")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub history_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub related_tag_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub r#type: TagRelationType,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::tag::Entity",
        from = "Column::RelatedTagId",
        to = "super::tag::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Tag,
    #[sea_orm(
        belongs_to = "super::tag_history::Entity",
        from = "Column::HistoryId",
        to = "super::tag_history::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    TagHistory,
}

impl Related<super::tag::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Tag.def()
    }
}

impl Related<super::tag_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TagHistory.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

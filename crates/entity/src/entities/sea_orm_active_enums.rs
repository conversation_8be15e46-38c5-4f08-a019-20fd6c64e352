//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(
    rs_type = "String",
    db_type = "Enum",
    enum_name = "AlternativeNameType"
)]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum AlternativeNameType {
    #[sea_orm(string_value = "Alias")]
    Alias,
    #[sea_orm(string_value = "Localization")]
    Localization,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "ArtistImageType")]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum ArtistImageType {
    #[sea_orm(string_value = "Profile")]
    Profile,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "ArtistType")]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum ArtistType {
    #[sea_orm(string_value = "Solo")]
    Solo,
    #[sea_orm(string_value = "Multiple")]
    Multiple,
    #[sea_orm(string_value = "Unknown")]
    Unknown,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "CommentState")]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum CommentState {
    #[sea_orm(string_value = "Visable")]
    Visable,
    #[sea_orm(string_value = "InReview")]
    InReview,
    #[sea_orm(string_value = "Hidden")]
    Hidden,
    #[sea_orm(string_value = "Deleted")]
    Deleted,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "CommentTarget")]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum CommentTarget {
    #[sea_orm(string_value = "Correction")]
    Correction,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "CorrectionStatus")]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum CorrectionStatus {
    #[sea_orm(string_value = "Pending")]
    Pending,
    #[sea_orm(string_value = "Approved")]
    Approved,
    #[sea_orm(string_value = "Rejected")]
    Rejected,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "CorrectionType")]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum CorrectionType {
    #[sea_orm(string_value = "Create")]
    Create,
    #[sea_orm(string_value = "Update")]
    Update,
    #[sea_orm(string_value = "Delete")]
    Delete,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(
    rs_type = "String",
    db_type = "Enum",
    enum_name = "CorrectionUserType"
)]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum CorrectionUserType {
    #[sea_orm(string_value = "Author")]
    Author,
    #[sea_orm(string_value = "Co-Author")]
    CoAuthor,
    #[sea_orm(string_value = "Reviewer")]
    Reviewer,
    #[sea_orm(string_value = "Approver")]
    Approver,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "DatePrecision")]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum DatePrecision {
    #[sea_orm(string_value = "Day")]
    Day,
    #[sea_orm(string_value = "Month")]
    Month,
    #[sea_orm(string_value = "Year")]
    Year,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "EntityType")]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum EntityType {
    #[sea_orm(string_value = "Artist")]
    Artist,
    #[sea_orm(string_value = "Label")]
    Label,
    #[sea_orm(string_value = "Release")]
    Release,
    #[sea_orm(string_value = "Song")]
    Song,
    #[sea_orm(string_value = "Tag")]
    Tag,
    #[sea_orm(string_value = "Event")]
    Event,
    #[sea_orm(string_value = "SongLyrics")]
    SongLyrics,
    #[sea_orm(string_value = "CreditRole")]
    CreditRole,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "ImageQueueStatus")]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum ImageQueueStatus {
    #[sea_orm(string_value = "Pending")]
    Pending,
    #[sea_orm(string_value = "Approved")]
    Approved,
    #[sea_orm(string_value = "Rejected")]
    Rejected,
    #[sea_orm(string_value = "Cancelled")]
    Cancelled,
    #[sea_orm(string_value = "Reverted")]
    Reverted,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "ReleaseType")]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum ReleaseType {
    #[sea_orm(string_value = "Album")]
    Album,
    #[sea_orm(string_value = "EP")]
    Ep,
    #[sea_orm(string_value = "Single")]
    Single,
    #[sea_orm(string_value = "Compilation")]
    Compilation,
    #[sea_orm(string_value = "Demo")]
    Demo,
    #[sea_orm(string_value = "Other")]
    Other,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "TagRelationType")]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum TagRelationType {
    #[sea_orm(string_value = "Inherit")]
    Inherit,
    #[sea_orm(string_value = "Derive")]
    Derive,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "TagType")]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum TagType {
    #[sea_orm(string_value = "Descriptor")]
    Descriptor,
    #[sea_orm(string_value = "Genre")]
    Genre,
    #[sea_orm(string_value = "Movement")]
    Movement,
    #[sea_orm(string_value = "Scene")]
    Scene,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(
    rs_type = "String",
    db_type = "Enum",
    enum_name = "release_image_type"
)]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum ReleaseImageType {
    #[sea_orm(string_value = "Cover")]
    Cover,
}
#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    EnumIter,
    DeriveActiveEnum,
    Serialize,
    Deserialize,
    Copy,
    enumset :: EnumSetType,
    utoipa :: ToSchema,
)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "storage_backend")]
#[enumset(no_super_impls)]
#[enumset(serialize_repr = "list")]
pub enum StorageBackend {
    #[sea_orm(string_value = "fs")]
    Fs,
}

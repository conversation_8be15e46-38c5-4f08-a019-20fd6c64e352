//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "credit_role_inheritance_history")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub history_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub super_id: i32,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::credit_role::Entity",
        from = "Column::SuperId",
        to = "super::credit_role::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    CreditRole,
    #[sea_orm(
        belongs_to = "super::credit_role_history::Entity",
        from = "Column::HistoryId",
        to = "super::credit_role_history::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    CreditRoleHistory,
}

impl Related<super::credit_role::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::CreditRole.def()
    }
}

impl Related<super::credit_role_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::CreditRoleHistory.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

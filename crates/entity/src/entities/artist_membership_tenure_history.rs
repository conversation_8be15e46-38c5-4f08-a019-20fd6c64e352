//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    <PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "artist_membership_tenure_history")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub membership_history_id: i32,
    pub join_year: Option<i16>,
    pub leave_year: Option<i16>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::artist_membership_history::Entity",
        from = "Column::MembershipHistoryId",
        to = "super::artist_membership_history::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    ArtistMembershipHistory,
}

impl Related<super::artist_membership_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistMembershipHistory.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

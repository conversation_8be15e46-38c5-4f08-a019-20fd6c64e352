//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "release_localized_title_history")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub history_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub language_id: i32,
    #[sea_orm(primary_key, auto_increment = false, column_type = "Text")]
    pub title: String,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::language::Entity",
        from = "Column::LanguageId",
        to = "super::language::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Language,
    #[sea_orm(
        belongs_to = "super::release_history::Entity",
        from = "Column::HistoryId",
        to = "super::release_history::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    ReleaseHistory,
}

impl Related<super::language::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Language.def()
    }
}

impl Related<super::release_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseHistory.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

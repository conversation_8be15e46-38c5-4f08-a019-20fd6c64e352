//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    <PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "artist_membership_role_history")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub membership_history_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub role_id: i32,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::artist_membership_history::Entity",
        from = "Column::MembershipHistoryId",
        to = "super::artist_membership_history::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    ArtistMembershipHistory,
    #[sea_orm(
        belongs_to = "super::credit_role::Entity",
        from = "Column::RoleId",
        to = "super::credit_role::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    CreditRole,
}

impl Related<super::artist_membership_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistMembershipHistory.def()
    }
}

impl Related<super::credit_role::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::CreditRole.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "release_catalog_number_history")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub history_id: i32,
    #[sea_orm(column_type = "Text")]
    pub catalog_number: String,
    pub label_id: Option<i32>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::label::Entity",
        from = "Column::LabelId",
        to = "super::label::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Label,
    #[sea_orm(
        belongs_to = "super::release_history::Entity",
        from = "Column::HistoryId",
        to = "super::release_history::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    ReleaseHistory,
}

impl Related<super::label::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Label.def()
    }
}

impl Related<super::release_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseHistory.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

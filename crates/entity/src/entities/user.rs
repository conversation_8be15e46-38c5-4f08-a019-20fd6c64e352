//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "user")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text", unique)]
    pub name: String,
    #[sea_orm(column_type = "Text")]
    pub password: String,
    pub avatar_id: Option<i32>,
    pub last_login: DateTimeWithTimeZone,
    pub profile_banner_id: Option<i32>,
    #[sea_orm(column_type = "Text", nullable)]
    pub bio: Option<String>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::comment::Entity")]
    Comment,
    #[sea_orm(has_many = "super::correction_revision::Entity")]
    CorrectionRevision,
    #[sea_orm(has_many = "super::correction_user::Entity")]
    CorrectionUser,
    #[sea_orm(
        belongs_to = "super::image::Entity",
        from = "Column::AvatarId",
        to = "super::image::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Image2,
    #[sea_orm(
        belongs_to = "super::image::Entity",
        from = "Column::ProfileBannerId",
        to = "super::image::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Image1,
    #[sea_orm(has_many = "super::user_list::Entity")]
    UserList,
    #[sea_orm(has_many = "super::user_role::Entity")]
    UserRole,
}

impl Related<super::comment::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Comment.def()
    }
}

impl Related<super::correction_revision::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::CorrectionRevision.def()
    }
}

impl Related<super::correction_user::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::CorrectionUser.def()
    }
}

impl Related<super::user_list::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserList.def()
    }
}

impl Related<super::user_role::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserRole.def()
    }
}

impl Related<super::correction::Entity> for Entity {
    fn to() -> RelationDef {
        super::correction_revision::Relation::Correction.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::correction_revision::Relation::User.def().rev())
    }
}

impl Related<super::role::Entity> for Entity {
    fn to() -> RelationDef {
        super::user_role::Relation::Role.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::user_role::Relation::User.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}

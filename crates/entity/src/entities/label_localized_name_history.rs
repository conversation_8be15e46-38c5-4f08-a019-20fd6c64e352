//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "label_localized_name_history")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub history_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub language_id: i32,
    #[sea_orm(primary_key, auto_increment = false, column_type = "Text")]
    pub name: String,
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::label_history::Entity",
        from = "Column::HistoryId",
        to = "super::label_history::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    LabelHistory,
    #[sea_orm(
        belongs_to = "super::language::Entity",
        from = "Column::LanguageId",
        to = "super::language::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Language,
}

impl Related<super::label_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::LabelHistory.def()
    }
}

impl Related<super::language::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Language.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

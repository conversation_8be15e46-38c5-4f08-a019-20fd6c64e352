//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    <PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "artist_link_history")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub history_id: i32,
    #[sea_orm(column_type = "Text")]
    pub url: String,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::artist_history::Entity",
        from = "Column::HistoryId",
        to = "super::artist_history::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    ArtistHistory,
}

impl Related<super::artist_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistHistory.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

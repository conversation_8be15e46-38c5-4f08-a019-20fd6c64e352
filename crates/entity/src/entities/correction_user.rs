//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::CorrectionUserType;

#[derive(
    <PERSON><PERSON>, <PERSON>bug, <PERSON>ialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "correction_user")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub correction_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub user_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub user_type: CorrectionUserType,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::correction::Entity",
        from = "Column::CorrectionId",
        to = "super::correction::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Correction,
    #[sea_orm(
        belongs_to = "super::user::Entity",
        from = "Column::UserId",
        to = "super::user::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    User,
}

impl Related<super::correction::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Correction.def()
    }
}

impl Related<super::user::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::User.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::DatePrecision;

#[derive(
    <PERSON><PERSON>, <PERSON>bug, <PERSON>ialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "label")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text")]
    pub name: String,
    pub founded_date: Option<Date>,
    pub founded_date_precision: DatePrecision,
    pub dissolved_date: Option<Date>,
    pub dissolved_date_precision: DatePrecision,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::label_founder::Entity")]
    LabelFounder,
    #[sea_orm(has_many = "super::label_localized_name::Entity")]
    LabelLocalizedName,
    #[sea_orm(has_many = "super::release_catalog_number::Entity")]
    ReleaseCatalogNumber,
    #[sea_orm(has_many = "super::release_catalog_number_history::Entity")]
    ReleaseCatalogNumberHistory,
}

impl Related<super::label_founder::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::LabelFounder.def()
    }
}

impl Related<super::label_localized_name::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::LabelLocalizedName.def()
    }
}

impl Related<super::release_catalog_number::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseCatalogNumber.def()
    }
}

impl Related<super::release_catalog_number_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseCatalogNumberHistory.def()
    }
}

impl Related<super::artist::Entity> for Entity {
    fn to() -> RelationDef {
        super::label_founder::Relation::Artist.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::label_founder::Relation::Label.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}

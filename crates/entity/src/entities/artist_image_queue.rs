//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::ArtistImageType;

#[derive(
    <PERSON>lone, Debug, <PERSON>ialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "artist_image_queue")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub artist_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub queue_id: i32,
    pub r#type: ArtistImageType,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::artist::Entity",
        from = "Column::ArtistId",
        to = "super::artist::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Artist,
    #[sea_orm(
        belongs_to = "super::image_queue::Entity",
        from = "Column::QueueId",
        to = "super::image_queue::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    ImageQueue,
}

impl Related<super::artist::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Artist.def()
    }
}

impl Related<super::image_queue::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ImageQueue.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

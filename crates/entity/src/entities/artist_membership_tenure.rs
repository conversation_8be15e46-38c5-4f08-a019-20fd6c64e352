//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "artist_membership_tenure")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub membership_id: i32,
    pub join_year: Option<i16>,
    pub leave_year: Option<i16>,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::artist_membership::Entity",
        from = "Column::MembershipId",
        to = "super::artist_membership::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    ArtistMembership,
}

impl Related<super::artist_membership::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistMembership.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "release_credit_history")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub artist_id: i32,
    pub history_id: i32,
    pub role_id: i32,
    pub on: Option<Vec<i16>>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::artist::Entity",
        from = "Column::ArtistId",
        to = "super::artist::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Artist,
    #[sea_orm(
        belongs_to = "super::credit_role::Entity",
        from = "Column::RoleId",
        to = "super::credit_role::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    CreditRole,
    #[sea_orm(
        belongs_to = "super::release_history::Entity",
        from = "Column::HistoryId",
        to = "super::release_history::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    ReleaseHistory,
}

impl Related<super::artist::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Artist.def()
    }
}

impl Related<super::credit_role::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::CreditRole.def()
    }
}

impl Related<super::release_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseHistory.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

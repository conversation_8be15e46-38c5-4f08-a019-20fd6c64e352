//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "song_credit")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub song_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub artist_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub role_id: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::artist::Entity",
        from = "Column::ArtistId",
        to = "super::artist::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Artist,
    #[sea_orm(
        belongs_to = "super::credit_role::Entity",
        from = "Column::RoleId",
        to = "super::credit_role::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    CreditRole,
    #[sea_orm(
        belongs_to = "super::song::Entity",
        from = "Column::SongId",
        to = "super::song::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Song,
}

impl Related<super::artist::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Artist.def()
    }
}

impl Related<super::credit_role::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::CreditRole.def()
    }
}

impl Related<super::song::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Song.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    <PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "artist_alias")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub first_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub second_id: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::artist::Entity",
        from = "Column::FirstId",
        to = "super::artist::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Artist2,
    #[sea_orm(
        belongs_to = "super::artist::Entity",
        from = "Column::SecondId",
        to = "super::artist::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Artist1,
}

impl ActiveModelBehavior for ActiveModel {}

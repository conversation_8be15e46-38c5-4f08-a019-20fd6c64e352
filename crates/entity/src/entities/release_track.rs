//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    <PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "release_track")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub release_id: i32,
    pub song_id: i32,
    #[sea_orm(column_type = "Text", nullable)]
    pub track_number: Option<String>,
    #[sea_orm(column_type = "Text", nullable)]
    pub display_title: Option<String>,
    pub duration: Option<String>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::release::Entity",
        from = "Column::ReleaseId",
        to = "super::release::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Release,
    #[sea_orm(has_many = "super::release_track_artist::Entity")]
    ReleaseTrackArtist,
    #[sea_orm(
        belongs_to = "super::song::Entity",
        from = "Column::SongId",
        to = "super::song::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Song,
}

impl Related<super::release::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Release.def()
    }
}

impl Related<super::release_track_artist::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseTrackArtist.def()
    }
}

impl Related<super::song::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Song.def()
    }
}

impl Related<super::artist::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_track_artist::Relation::Artist.def()
    }
    fn via() -> Option<RelationDef> {
        Some(
            super::release_track_artist::Relation::ReleaseTrack
                .def()
                .rev(),
        )
    }
}

impl ActiveModelBehavior for ActiveModel {}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    <PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "artist_membership")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub member_id: i32,
    pub group_id: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bug, Enum<PERSON>ter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::artist::Entity",
        from = "Column::GroupId",
        to = "super::artist::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Artist2,
    #[sea_orm(
        belongs_to = "super::artist::Entity",
        from = "Column::MemberId",
        to = "super::artist::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Artist1,
    #[sea_orm(has_many = "super::artist_membership_role::Entity")]
    ArtistMembershipRole,
    #[sea_orm(has_many = "super::artist_membership_tenure::Entity")]
    ArtistMembershipTenure,
}

impl Related<super::artist_membership_role::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistMembershipRole.def()
    }
}

impl Related<super::artist_membership_tenure::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistMembershipTenure.def()
    }
}

impl Related<super::credit_role::Entity> for Entity {
    fn to() -> RelationDef {
        super::artist_membership_role::Relation::CreditRole.def()
    }
    fn via() -> Option<RelationDef> {
        Some(
            super::artist_membership_role::Relation::ArtistMembership
                .def()
                .rev(),
        )
    }
}

impl ActiveModelBehavior for ActiveModel {}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "credit_role_history")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text")]
    pub name: String,
    #[sea_orm(column_type = "Text")]
    pub short_description: String,
    #[sea_orm(column_type = "Text")]
    pub description: String,
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::credit_role_inheritance_history::Entity")]
    CreditRoleInheritanceHistory,
}

impl Related<super::credit_role_inheritance_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::CreditRoleInheritanceHistory.def()
    }
}

impl Related<super::credit_role::Entity> for Entity {
    fn to() -> RelationDef {
        super::credit_role_inheritance_history::Relation::CreditRole.def()
    }
    fn via() -> Option<RelationDef> {
        Some(
            super::credit_role_inheritance_history::Relation::CreditRoleHistory
                .def()
                .rev(),
        )
    }
}

impl ActiveModelBehavior for ActiveModel {}

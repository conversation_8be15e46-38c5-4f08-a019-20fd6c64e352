//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "correction_revision")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub correction_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub entity_history_id: i32,
    pub author_id: i32,
    #[sea_orm(column_type = "Text")]
    pub description: String,
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::correction::Entity",
        from = "Column::CorrectionId",
        to = "super::correction::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Correction,
    #[sea_orm(
        belongs_to = "super::user::Entity",
        from = "Column::AuthorId",
        to = "super::user::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    User,
}

impl Related<super::correction::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Correction.def()
    }
}

impl Related<super::user::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::User.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

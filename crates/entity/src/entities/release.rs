//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::{DatePrecision, ReleaseType};

#[derive(
    <PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "release")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text")]
    pub title: String,
    pub release_type: ReleaseType,
    pub release_date: Option<Date>,
    pub release_date_precision: DatePrecision,
    pub recording_date_start: Option<Date>,
    pub recording_date_start_precision: DatePrecision,
    pub recording_date_end: Option<Date>,
    pub recording_date_end_precision: DatePrecision,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::release_artist::Entity")]
    ReleaseArtist,
    #[sea_orm(has_many = "super::release_catalog_number::Entity")]
    ReleaseCatalogNumber,
    #[sea_orm(has_many = "super::release_credit::Entity")]
    ReleaseCredit,
    #[sea_orm(has_many = "super::release_event::Entity")]
    ReleaseEvent,
    #[sea_orm(has_many = "super::release_image::Entity")]
    ReleaseImage,
    #[sea_orm(has_many = "super::release_image_queue::Entity")]
    ReleaseImageQueue,
    #[sea_orm(has_many = "super::release_localized_title::Entity")]
    ReleaseLocalizedTitle,
    #[sea_orm(has_many = "super::release_track::Entity")]
    ReleaseTrack,
}

impl Related<super::release_artist::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseArtist.def()
    }
}

impl Related<super::release_catalog_number::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseCatalogNumber.def()
    }
}

impl Related<super::release_credit::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseCredit.def()
    }
}

impl Related<super::release_event::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseEvent.def()
    }
}

impl Related<super::release_image::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseImage.def()
    }
}

impl Related<super::release_image_queue::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseImageQueue.def()
    }
}

impl Related<super::release_localized_title::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseLocalizedTitle.def()
    }
}

impl Related<super::release_track::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseTrack.def()
    }
}

impl Related<super::artist::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_artist::Relation::Artist.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::release_artist::Relation::Release.def().rev())
    }
}

impl Related<super::event::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_event::Relation::Event.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::release_event::Relation::Release.def().rev())
    }
}

impl Related<super::image::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_image::Relation::Image.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::release_image::Relation::Release.def().rev())
    }
}

impl Related<super::image_queue::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_image_queue::Relation::ImageQueue.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::release_image_queue::Relation::Release.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::StorageBackend;

#[derive(
    <PERSON>lone, Debug, <PERSON>ialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "image")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text")]
    pub filename: String,
    #[sea_orm(column_type = "Text")]
    pub directory: String,
    pub uploaded_by: i32,
    pub uploaded_at: DateTimeWithTimeZone,
    pub backend: StorageBackend,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::artist_image::Entity")]
    ArtistImage,
    #[sea_orm(has_many = "super::image_queue::Entity")]
    ImageQueue,
    #[sea_orm(has_many = "super::release_image::Entity")]
    ReleaseImage,
    #[sea_orm(
        belongs_to = "super::user::Entity",
        from = "Column::UploadedBy",
        to = "super::user::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    User,
}

impl Related<super::artist_image::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ArtistImage.def()
    }
}

impl Related<super::image_queue::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ImageQueue.def()
    }
}

impl Related<super::release_image::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseImage.def()
    }
}

impl Related<super::user::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::User.def()
    }
}

impl Related<super::artist::Entity> for Entity {
    fn to() -> RelationDef {
        super::artist_image::Relation::Artist.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::artist_image::Relation::Image.def().rev())
    }
}

impl Related<super::release::Entity> for Entity {
    fn to() -> RelationDef {
        super::release_image::Relation::Release.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::release_image::Relation::Image.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}

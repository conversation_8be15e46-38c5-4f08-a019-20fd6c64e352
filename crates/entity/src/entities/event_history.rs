//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

use super::sea_orm_active_enums::DatePrecision;

#[derive(
    <PERSON><PERSON>, Debug, <PERSON>ialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "event_history")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(column_type = "Text")]
    pub name: String,
    #[sea_orm(column_type = "Text")]
    pub short_description: String,
    #[sea_orm(column_type = "Text")]
    pub description: String,
    pub start_date: Option<Date>,
    pub start_date_precision: DatePrecision,
    pub end_date: Option<Date>,
    pub end_date_precision: DatePrecision,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::event_alternative_name_history::Entity")]
    EventAlternativeNameHistory,
}

impl Related<super::event_alternative_name_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::EventAlternativeNameHistory.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

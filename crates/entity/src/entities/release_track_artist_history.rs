//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.10

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(
    <PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize,
)]
#[sea_orm(table_name = "release_track_artist_history")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub track_history_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub artist_id: i32,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::artist::Entity",
        from = "Column::ArtistId",
        to = "super::artist::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    Artist,
    #[sea_orm(
        belongs_to = "super::release_track_history::Entity",
        from = "Column::TrackHistoryId",
        to = "super::release_track_history::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    ReleaseTrackHistory,
}

impl Related<super::artist::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Artist.def()
    }
}

impl Related<super::release_track_history::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReleaseTrackHistory.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
